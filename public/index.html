<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
  <link rel="icon" href="data:image/ico;base64,aWNv">
  <title><%= webpackConfig.name %></title>
  <!-- require cdn assets css -->
  <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
  <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
  <% } %>
  <script>
    // ie兼容性问题
    if (!window.location.origin) {
      window.location.origin = window.location.protocol + "//" + window.location.hostname + (window.location.port ? ':' + window.location.port: '');
    }
  </script>
</head>
<body>
<noscript>
  <strong>We're sorry but vue-antd-pro doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>
<div id="mpElectronicGovernment"></div>
<!--browser detection for > ie-->
<script async src="//img.yeepay.com/fe-resources/sdk/mp-browser-support-detector/no-ie.min.js" type="text/javascript"></script>
<!--webfunny probe-->
<script async src="//img.yeepay.com/fe-resources/webfunny-probe/mp-dashboard-newV2/index.min.js" type="text/javascript"></script>

<!-- require cdn assets js -->
<% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
<script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
<% } %>
</body>
</html>
