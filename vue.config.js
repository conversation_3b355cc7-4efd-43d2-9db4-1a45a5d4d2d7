const { name } = require('./package')
const path = require('path')
const webpack = require('webpack')
const GitRevisionPlugin = require('git-revision-webpack-plugin')
const GitRevision = new GitRevisionPlugin()
const buildDate = JSON.stringify(new Date().toLocaleString())
const createThemeColorReplacerPlugin = require('./config').createThemeColorReplacerPlugin
console.log(createThemeColorReplacerPlugin)
const { proxy } = require('./config')

const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')

function resolve (dir) {
  return path.join(__dirname, dir)
}

// check Gitd
function getGitHash () {
  try {
    return GitRevision.version()
  } catch (e) {}
  return 'unknown'
}

const isProd = process.env.NODE_ENV === 'production'
const assetsCDN = {
  // webpack build externals
  externals: {
    vue: 'Vue',
    'vue-router': 'VueRouter',
    vuex: 'Vuex',
    axios: 'axios'
  },
  css: [],
  // https://unpkg.com/browse/vue@2.6.10/
  js: [
    '//img.yeepay.com/fe-libs/vue/2.6.10/vue.min.js',
    '//img.yeepay.com/fe-libs/vue-router/3.1.3/vue-router.min.js',
    '//img.yeepay.com/fe-libs/vuex/3.1.1/vuex.min.js',
    '//img.yeepay.com/fe-libs/axios/0.19.0/axios.min.js'
  ]
}
const plugins = [
  // Ignore all locale files of moment.js
  new webpack.IgnorePlugin({
    resourceRegExp: /^\.\/locale$/,
    contextRegExp: /moment$/
  }),
  new webpack.DefinePlugin({
    APP_VERSION: `"${require('./package.json').version}"`,
    GIT_HASH: JSON.stringify(getGitHash()),
    BUILD_DATE: buildDate
  })
]
if (process.env.NODE_ENV === 'analyze') {
  plugins.push(new SpeedMeasurePlugin())
}

if (process.env.NODE_ENV === 'analyze') {
  plugins.push(new BundleAnalyzerPlugin())
}

// vue.config.js
const vueConfig = {
  publicPath: process.env.NODE_ENV === 'development' ? `/` : './',
  assetsDir: 'statics',
  // 自定义webpack配置
  configureWebpack: {
    // webpack plugins
    plugins,
    module: {
      rules: [
        {
          test: /\.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto'
        }
        // Other rules...
      ]
    },
    resolve: {
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.mjs']
    },
    output: {
      library: `${name}-[name]`,
      libraryTarget: 'umd', // 把子应用打包成 umd 库格式
      jsonpFunction: `webpackJsonp_${name}`
    },
    // if prod, add externals
    externals: isProd ? assetsCDN.externals : {}
  },

  chainWebpack: (config) => {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('_', resolve('src'))

    const svgRule = config.module.rule('svg')
    svgRule.uses.clear()
    svgRule
      .oneOf('inline')
      .resourceQuery(/inline/)
      .use('vue-svg-icon-loader')
      .loader('vue-svg-icon-loader')
      .end()
      .end()
      .oneOf('external')
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: 'assets/[name].[hash:8].[ext]'
      })
    config.module
      .rule('es6+js')
      .test(/\.[cm]?js$/)
      .include
      .add(/node_modules\/change-case/)
      .add(/node_modules\/@yeepay/)
      .add(/node_modules\/radash/)
      .add(/node_modules\/superstruct/)
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .options({
        presets: ['@babel/preset-env'],
        plugins: ['@babel/plugin-transform-runtime']
      })
      .end()

    // if prod is on
    // assets require on cdn
    if (isProd) {
      config.plugin('html').tap(args => {
        args[0].cdn = assetsCDN
        return args
      })
    }
    // 低代码拆包
    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config.optimization.splitChunks({
        chunks: 'all',
        maxInitialRequests: 20,
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          'ant-design-vue': {
            name: 'ant-design-vue',
            test: /[\\/]node_modules[\\/]ant-design-vue(.*)/,
            priority: 30
          },
          'lowcode-core': {
            name: 'lowcode-core',
            test: /[\\/]node_modules[\\/]@yeepay[\\/]lowcode-renderer/,
            priority: 20
          },
          'lowcode-charts': {
            name: 'lowcode-charts',
            test: /[\\/]node_modules[\\/](?:@yeepay[\\/]lowcode-charts)/,
            priority: 11
          },
          'lowcode-materials': {
            name: 'lowcode-materials',
            test: /[\\/]node_modules[\\/](?:@yeepay[\\/].*?-materials|@yeepay[\\/]lowcode-.*)/,
            priority: 10
          },
          'antv-g2': {
            name: 'antv-g2',
            test: /[\\/]node_modules[\\/](?:@ant-design[\\/].*|@antv[\\/]g2.*)/,
            priority: 10
          },
          commons: {
            name: 'chunk-commons',
            test: resolve(`src/components`), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })
      config.optimization.runtimeChunk(true)
    })
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          // less vars，customize ant design theme
          'primary-color': '#52BF63',
          // 'link-color': '#F5222D',
          'border-radius-base': '2px'
        },
        // DO NOT REMOVE THIS LINE
        javascriptEnabled: true
      }

    }
  },

  devServer: {
    host: '0.0.0.0',
    port: 8013,
    disableHostCheck: true,
    // If you want to turn on the proxy, please remove the mockjs /src/main.jsL11
    proxy: proxy,
    headers: {
      'Access-Control-Allow-Origin': '*'
    }
  },

  // disable source map in production
  productionSourceMap: false,
  lintOnSave: undefined,
  // babel-loader no-ignore node_modules/*
  transpileDependencies: []
}

// preview.pro.loacg.com only do not use in your production;
if (process.env.VUE_APP_PREVIEW === 'true') {
  console.log('VUE_APP_PREVIEW', true)
  // add `ThemeColorReplacer` plugin to webpack plugins
  // vueConfig.configureWebpack.plugins.push(createThemeColorReplacerPlugin())
}
module.exports = vueConfig
