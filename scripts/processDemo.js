console.log('init.js')
// 引入所需模块
const fs = require('fs')
const path = require('path')

// 将 __dirname 改为根路径
const rootPath = path.resolve(__dirname, '..')

// 获取命令行参数
const projectName = process.argv[2]

// 定义要替换的文件路径和内容
const filesToReplace = [
  {
    filePath: path.join(rootPath, 'package.json'),
    replaceAll: [
      {
        newContent: `"name": "${projectName}"`,
        regExp: /"name": ".*"/
      },
      {
        newContent: `"description": "老板管账-${projectName}"`,
        regExp: /"description": ".*"/
      }
    ]
  },
  {
    filePath: path.join(rootPath, 'src', 'App.vue'),
    replaceAll: [
      {
        newContent: projectName,
        regExp: /mp-demo/g
      }
    ]
  },
  {
    filePath: path.join(rootPath, 'src', 'main.ts'),
    replaceAll: [
      {
        newContent: projectName,
        regExp: /mp-demo/g
      }
    ]
  }
]

// 函数：替换文件内容
function replaceFileContent(filePath, replaceAll) {
  // 读取文件内容
  const content = fs.readFileSync(filePath, 'utf8')
  // 替换内容
  let _content = content
  _content = replaceAll.reduce((prev, { newContent, regExp }) => {
    return prev.replace(regExp, newContent)
  }, _content)
  // 写入文件
  fs.writeFile(filePath, _content, 'utf8', (err) => {
    if (err) {
      console.error(`无法写入文件 ${filePath}:`, err)
    } else {
      console.log(`文件 ${filePath} 已成功更新`)
    }
  })
}

// 遍历文件列表并替换内容
filesToReplace.forEach((file) => {
  replaceFileContent(file.filePath, file.replaceAll)
})

// 文件移除
// 定义要删除的文件路径
const filesToDelete = [
  // path.join(rootPath, 'src', 'App.vue')
]

// 删除文件
filesToDelete.forEach(filePath => {
  fs.unlink(filePath, err => {
    if (err) {
      console.error(`删除文件出错: ${filePath}`, err)
    } else {
      console.log(`文件已删除: ${filePath}`)
    }
  })
})

// 文件夹移除
// 定义要删除的文件夹路径
const foldersToDelete = [
  // path.join(rootPath, 'src', 'views', 'myManage')
]

// 删除文件夹
foldersToDelete.forEach(folderPath => {
  console.log('folderPath:', folderPath)
  fs.rmdir(folderPath, { recursive: true }, err => {
    if (err) {
      console.error(`删除文件夹出错: ${folderPath}`, err)
    } else {
      console.log(`文件夹已删除: ${folderPath}`)
    }
  })
})
