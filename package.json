{"name": "mp-electronic-government", "description": "老板管账-mp-electronic-government", "version": "1.0.0", "main": "main.ts", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:analyze": "vue-cli-service build --mode analyze", "lint": "eslint --ext .js,.vue .", "init": "node ./scripts/processDemo.js", "prepare": "husky"}, "dependencies": {"@antv/data-set": "^0.10.2", "@vue/composition-api": "^1.7.2", "@yeepay/antd-materials": "^2.24.18-beta.4", "@yeepay/client-utils": "1.2.2", "@yeepay/lowcode-renderer": "2.24.3", "@yeepay/vue-i18n": "^2.0.0", "ant-design-vue": "^1.7.8", "apollo-boost": "^0.4.9", "autoprefixer": "8.0.0", "axios": "^0.27.2", "babel-loader": "^8.4.1", "babel-runtime": "^6.26.0", "clipboard": "^2.0.11", "core-js": "^3.38.1", "echarts": "^4.9.0", "enquire.js": "^2.1.6", "graphql": "^15.3.0", "graphql-tag": "^2.11.0", "husky": "^9.1.7", "jquery": "^3.5.1", "js-cookie": "^2.2.1", "jsencrypt": "^3.0.0-rc.1", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.3.0", "mockjs2": "1.0.8", "moment": "^2.30.1", "nprogress": "^0.2.0", "postcss-loader": "^8.1.1", "qs": "^6.13.0", "sortablejs": "^1.15.3", "store": "^2.0.12", "tailwindcss": "^1.9.6", "viser-vue": "^2.4.8", "vue": "2.6.14", "vue-apollo": "^3.1.2", "vue-class-component": "^7.2.6", "vue-clipboard2": "^0.2.1", "vue-container-query": "^0.1.0", "vue-copy-to-clipboard": "^1.0.3", "vue-cropper": "0.4.9", "vue-fullscreen": "^2.6.1", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.6.5", "vue-svg-component-runtime": "^1.0.1", "vuejs-fragment": "^1.0.1", "vuex": "^3.6.2", "wangeditor": "^3.1.1"}, "devDependencies": {"@ant-design/colors": "^3.2.2", "@babel/plugin-transform-runtime": "^7.25.4", "@babel/preset-env": "^7.25.4", "@types/jest": "^24.9.1", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@vue/cli-plugin-babel": "^4.5.19", "@vue/cli-plugin-eslint": "^4.5.19", "@vue/cli-plugin-router": "^4.5.19", "@vue/cli-plugin-typescript": "^4.5.19", "@vue/cli-plugin-unit-jest": "^4.5.19", "@vue/cli-plugin-vuex": "^4.5.19", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-standard": "^4.0.0", "@vue/eslint-config-typescript": "^5.1.0", "@vue/test-utils": "^1.3.6", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.13.8", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "^4.1.2", "eslint": "^6.8.0", "eslint-plugin-html": "^5.0.0", "eslint-plugin-vue": "^6.2.2", "file-loader": "^4.2.0", "git-revision-webpack-plugin": "^3.0.6", "handlebars": "^4.7.6", "less": "^3.13.1", "less-loader": "^5.0.0", "lint-staged": "^10.5.1", "opencollective": "^1.0.3", "opencollective-postinstall": "^2.0.3", "rimraf": "^3.0.2", "speed-measure-webpack-plugin": "^1.5.0", "typescript": "5.5.4", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "2.6.14", "webpack-bundle-analyzer": "^4.10.2", "webpack-theme-color-replacer": "1.3.12"}, "license": "ISC", "resolutions": {"nanopop": "2.1.0", "commander": "11.1.0", "webpack": "4.47.0", "@achrinza/node-ipc": "^9.2.2"}}