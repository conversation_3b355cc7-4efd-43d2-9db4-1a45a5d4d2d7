import { ACCESS_TOKEN, TOKEN_COOKIE_EXPIRES } from '@/store/mutation-types'
import { getQueryObject } from '@/utils/util'
import storage from 'store'

// url has token ?
let urlToken = getQueryObject(window.location.href).yuiassotoken
if (urlToken) {
  const hashIndex = urlToken.indexOf('#')
  if (hashIndex !== -1) {
    urlToken = urlToken.slice(0, hashIndex)
  }
  urlToken = 'Bearer ' + urlToken
  storage.set(ACCESS_TOKEN, urlToken)
}

export function getToken () {
  return urlToken || storage.get(ACCESS_TOKEN)
}

export function setToken (token, rememberMe) {
  if (rememberMe) {
    return storage.set(ACCESS_TOKEN, token, TOKEN_COOKIE_EXPIRES)
  } else return storage.set(ACCESS_TOKEN, token)
}

export function removeToken () {
  return storage.remove(ACCESS_TOKEN)
}
