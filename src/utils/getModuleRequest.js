import { createService } from '@/utils/http'
import store from '@/store'
import safetyModal from '@/components/SafetyModal'
import qs from 'qs'

function moduleReqMethods(baseUrl) {
  const service = createService(baseUrl)
  function requestMiddle(options) {
    const { url, method, params, verify } = options
    const safetyMap = store.state.permission.safetyMap
    const safetyUrl = `${method.toUpperCase()}#${baseUrl}${url}`
    const config = {}
    if (safetyMap[safetyUrl] && verify) {
      if (window.sessionStorage.getItem('safetyToken')) {
        params['token'] = window.sessionStorage.getItem('safetyToken')
      } else {
        return safetyModal.showSafetyModal(safetyMap[safetyUrl]).then(() => {
          if (method === 'get') {
            if (params) {
              config.url = url + '?' + qs.stringify(params, { indices: false })
            } else {
              config.url = url
            }
            config.method = 'get'
          } else {
            config.url = url
            config.method = 'post'
            config.data = params
          }
          return service(config)
        })
      }
    }
    if (method === 'get') {
      if (params) {
        config.url = url + '?' + qs.stringify(params, { indices: false })
      } else {
        config.url = url
      }
      config.method = 'get'
    } else {
      config.url = url
      config.method = 'post'
      config.data = params
    }
    return service(config)
  }
  function get(url, params, verify = true) {
    return requestMiddle({
      url,
      method: 'get',
      params,
      verify
    })
  }
  function post(url, params, verify = true) {
    return requestMiddle({
      url,
      params,
      method: 'post',
      verify
    })
  }
  return { get, post }
}
export default moduleReqMethods
