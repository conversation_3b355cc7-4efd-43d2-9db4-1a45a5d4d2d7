
import { cloneDeep } from 'lodash'

// 测试数据
const mockData = [
  {
    'groupProductName': '预消费商家扫码_支付宝特殊',
    'productInfos': [
      {
        'productCode': 'PRECONSUME_MERCHANT_SCAN_ALIPAY_LARGE',
        'productName': '预消费商家扫码_支付宝特殊',
        'percentRate': '1',
        'fixedRate': '',
        'rebateRatio': '1'
      }
    ],
    'feeChargingWay': 'LADDER_NO_ACC',
    'percentRate': '1',
    'fixedRate': '',
    'ladderFeeList': [
      {
        'rateType': 'SINGLE_FIXED',
        'fixedRate': '10',
        'ladderMin': '0',
        'ladderMax': '11'
      },
      {
        'rateType': 'PERCENT_MIX_CAP',
        'percentRate': '1',
        'fixedRate': '2',
        'ladderMin': '11',
        'ladderMax': '22'
      },
      {
        'rateType': 'PERCENT_MIX_CAP',
        'percentRate': '1',
        'fixedRate': '2',
        'ladderMin': '22',
        'ladderMax': '33'
      },
      {
        'rateType': 'PERCENT_MIX_CAP',
        'percentRate': '1',
        'fixedRate': '3',
        'ladderMin': '33'
      }
    ]
  },
  {
    'groupProductName': '预消费商家扫码_支付宝特殊_借记卡',
    'productInfos': [
      {
        'productCode': 'PRECONSUME_MERCHANT_SCAN_ALIPAY_LARGE_DEBIT',
        'productName': '预消费商家扫码_支付宝特殊_借记卡',
        'percentRate': '1',
        'fixedRate': '',
        'rebateRatio': '1'
      }
    ],
    'feeChargingWay': 'SINGLE_PERCENT',
    'percentRate': '1',
    'fixedRate': ''
  }
]

function createRenderLadderList(list, data) {
  const res = list.map((item, index) => {
    const amountMin = index > 0 ? `(含)${item.ladderMin}元` : `${item.ladderMin || 0}元`
    const amountMax = item.ladderMax ? `${item.ladderMax}元` : '不限'
    const map = {
      groupProductName: `${amountMin} ~ ${amountMax}`,
      selected: data.selected,
      ...item
    }
    return map
  })
  return res
}

export function formatProductList(treeData) {
  const tree = cloneDeep(treeData)
  let key = 0
  function traverse(list = [], level = 1) {
    list.forEach(item => {
      item.key = key++

      if (item.fixedRate) {
        item.oldFixedRate = item.fixedRate
      }

      if (item.percentRate) {
        item.oldPercentRate = item.percentRate
      }

      if (item.minRate) {
        item.oldMinRate = item.minRate
      }

      item.level = level
      if (Array.isArray(item.ladderFeeList)) {
        item.ladderFeeList = traverse(item.ladderFeeList, level + 1)
        item.renderLadderList = createRenderLadderList(item.ladderFeeList, item)
      }
    })
    return list
  }
  return traverse(tree)
}

// 创建接口需要的费率参数
export function createRateListPayload(list) {
  const res = cloneDeep(list)
  res.forEach(item => {
    // 删除用于前端渲染的参数
    delete item.level
    delete item.key

    delete item.oldMd5
    delete item.newMd5
    delete item.oldFixedRate
    delete item.oldPercentRate
    delete item.oldMinRate
    if (Array.isArray(item.renderLadderList)) {
      item.renderLadderList.forEach(child => {
        // 删除用于前端渲染的参数
        delete child.selected
        delete child.level
        delete child.key
        delete child.groupProductName
        delete child.oldFixedRate
        delete child.oldPercentRate
        delete child.oldMinRate
      })
      item.ladderFeeList = cloneDeep(item.renderLadderList)
      delete item.renderLadderList
    }
  })
  return res
}

// mock data
export const mockProductList = formatProductList(mockData)
