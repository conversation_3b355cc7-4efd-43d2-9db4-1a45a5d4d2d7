export default function createSubmit(forbiddenCallback, notfoundCallback, failCallback) {
  function error(error) {
    let status = 0
    try {
      status = error.response.status || error.response.data.status
    } catch (e) {
      if (error.toString().indexOf('Error: timeout') !== -1) {
        return Promise.reject(error)
      }
    }
    if (status) {
      if (status === 403) {
        forbiddenCallback(error)
      } else if (status === 404) {
        notfoundCallback(error)
      } else {
        failCallback(error.response)
      }
    } else {
      failCallback(error.response)
    }
  }
  return error
}
