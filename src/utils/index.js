/**
 * method map to list<key, value>.
 * @param {Object} mapObj.
 * @return {Array} return Array list.
 */
export function mapToKeyValue(mapObj) {
  return Object.keys(mapObj).map(key => ({
    key,
    value: mapObj[key]
  }))
}

/**
 * method list of map to list<key, value>.
 * @param {Array} mapList.
 * @return {Array} return Array list.
 */
export function mapListToKeyValue(mapList) {
  return mapList.map(item => {
    const [key, value] = Object.entries(item)[0]
    return {
      key,
      value
    }
  })
}

/**
 * method map to form data.
 * @param {Object} mapObj.
 * @return {Object} return FormData.
 */
export function mapToFormData(mapObj) {
  const form = new FormData()
  for (const [key, value] of Object.entries(mapObj)) {
    form.append(key, value)
  }
  return form
}
/**
 * method map to form data.
 * @param {Object} mapObj.
 * @return {Object} return FormData.
 */
export function toggleWrapMask(type) {
  try {
    window.parent.toggleWrapMask(type)
  } catch (error) {
    // console.log(error)
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=#]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

export function getLocale() {
  const lang = getQueryObject().lang || ''
  return lang.replace(/-/g, '_')
}

export function getEnv() {
  const originList = ['https://boss.yeepay.com', 'https://mp.yeepay.com', 'https://uatmp.yeepay.com']
  const origin = window.location.origin
  const lowcodeEnv = window.localStorage.getItem('__yeepay_lowcode_environment__')
  let env = ''

  const ncEnable = ['nc', 'qa'].includes(lowcodeEnv)
  if (originList.includes(origin)) {
    if (ncEnable) {
      env = lowcodeEnv
    } else {
      env = 'prod'
    }
  } else if (origin === 'https://ncsrs.yeepay.com') {
    env = 'nc'
  } else {
    env = 'qa'
  }
  return env
}
