/**
 * @file 调用老商户后台函数集合
 * <AUTHOR>
 * @date 2021/2/24
 * @description Integration here for migrating to the new platform quickly in the future
 */

// 老商户后台某个页面
export function jumpToOldPlatForm(param) {
  window.parent.location = `${window.parent.location.origin}/account-pay-mboss/remitAudit/page/list?param=${param}`
}

export function jumpToDetail() {
  window.parent.postMessage('jumpToDetail', '*')
}

export function jumpToFundBill() {
  window.parent.postMessage('jumpToFundBill', '*')
}

export function openMask() {
  window.parent.postMessage('openMask', '*')
}

export function closeMask() {
  window.parent.postMessage('closeMask', '*')
}

/* 适用于 confirm 等关闭会立即消失蒙层的情况 */
export function closeMaskImmediately() {
  window.parent.postMessage('closeMaskImmediately', '*')
}
