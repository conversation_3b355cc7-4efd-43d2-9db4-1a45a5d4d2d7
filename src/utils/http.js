import message from 'ant-design-vue/es/message'
import { VueAxios } from './axios'
import submitFormError from './submitFormError'
import { serviceFactory } from '@yeepay/client-utils'
import { downloadFile } from '@/utils/util'
import { Modal } from 'ant-design-vue'
import Vue from 'vue'
import { sucessCodes } from '@/utils/request'

function isBlob(data) {
  return Object.prototype.toString.call(data) === '[object Blob]'
}

function successCallback(response) {
  // [特殊处理] 若为文件流, 则直接下载该文件
  if (response.data.code === undefined || response.data.code === null) {
    const disposition = response.headers['content-disposition']
    if (disposition) {
      const fileinfo = disposition.split(';')[1]
      const filename = decodeURIComponent(fileinfo.split('=')[1])
      downloadFile(response.data, filename.split('.')[0], filename.split('.')[1])
    } else {
      if (isBlob(response.data)) {
        var reader = new FileReader()
        reader.readAsText(response.data, 'utf-8')
        reader.onload = function() {
          // 读取完毕后输出结果
          const resultObj = JSON.parse(this.result)
          // 兼容特殊情况处理（JSON or 二进制流的接口）因低代码文件流模式，返回结果会是为isBlob类型，故需要如下特殊处理JSON情况
          if (sucessCodes.includes(resultObj.code)) {
            if (!(resultObj?.data?.downloadJsonflag === 'download')) {
              return
            }
            const origin = window.location.origin
            const data = resultObj.data
            // 下载为空 code:000000,:data:{"downloadJsonflag":"download",code:"001"}
            // 下载失败 code:999999,msg:"" data:{"downloadJsonflag":"download"}
            // 任务中心下载：code:000000 data:{"downloadJsonflag":"download",code:"000"}
            // 小于一万条 直接文件流响应
            if (data.code === '001') {
              // 下载为空，只提供关闭按钮
              Modal.info({
                title: '提示',
                content: '下载内容为空',
                centered: true,
                okText: '关闭'
              })
            } else if (data.code === '000') {
              Modal.confirm({
                title: '文件生成中',
                icon: (h) =>
                  h('a-icon', {
                    props: {
                      type: 'info-circle',
                      theme: 'filled'
                    },
                    style: {
                      color: '#499CFF'
                    }
                  }),
                content: `请到下载中心查看文件`,
                autoFocusButton: null,
                okText: '立即前往',
                cancelText: '我知道了',
                class: 'jump-to-download-center',
                onOk() {
                  try {
                    if (Vue.prototype.parentProps && Vue.prototype.parentProps.actions && Vue.prototype.parentProps.actions.setGlobalState) {
                      Vue.prototype.parentProps.actions.setGlobalState({
                        toAnotherMenu: '/mp-account/#/accountManage/downloadCenter',
                        topParentModule: '/mp-account',
                        topParentName: '账号管理'
                      })
                    } else {
                      window.open(`${origin}/mp-galaxy/mp-account/#/accountManage/downloadCenter`)
                    }
                  } catch (error) {
                    window.open(`${origin}/mp-galaxy/mp-account/#/accountManage/downloadCenter`)
                  }
                }
              })
            }
          } else {
            message.error(resultObj.message || resultObj.msg || resultObj.errMsg || resultObj?.data?.errMsg || '系统异常，请稍后重试')
          }
        }
      }
    }

    // 老后台重新登录会返回登录页面
    if (response.headers['content-type'] === 'text/html;charset=UTF-8') {
      parent.window.location.reload()
    }
  }
}

function failCallback(response) {
  const { status } = response
  if (status >= 500 && status < 600) {
    message.error('500～600  系统异常请稍后重试！')
  } else {
    // 有的页面有自定义的弹窗提示，不需要这个默认的
    if (!response.data.selfTip) {
      const msg = response.data.message || response.data.msg
      if (msg) {
        message.error(h => {
          return h('span', { domProps: { innerHTML: msg } })
        })
      } else {
        message.error('请求失败')
      }
    }
  }
}

function unauthorizedCallback(response) {
  // 不在子应用做失效跳登录的逻辑,直接刷页面触发主应用的接口失效跳登录
  window.location.reload()
}

function forbiddenCallback(response) {
  const { data } = response
  message.error(data.message || '该功能无权限')
  return Promise.reject(response)
}

function notfoundCallback(response) {
  message.error({
    content: '接口未找到'
  })
}

export function createService(baseUrl, code) {
  const options = {
    baseUrl,
    code,
    headers: {
      'yp-request-id': 'uuid',
      systemcode: 'boss-noah',
      'Cache-Control': 'no-cache',
      'X-WX-Id': window.localStorage.getItem('X-WX-Id') || '',
      'interaction-type': 'ERROR_CODE'
    }
  }
  return serviceFactory(
    options,
    successCallback,
    failCallback,
    unauthorizedCallback,
    forbiddenCallback,
    notfoundCallback
  )
}

const service = serviceFactory(
  {
    baseUrl: '/merchant-portal-server',
    headers: {
      systemcode: 'boss-noah',
      'Cache-Control': 'no-cache',
      'yp-request-id': 'uuid',
      'X-WX-Id': window.localStorage.getItem('X-WX-Id') || '',
      'interaction-type': 'ERROR_CODE'
    }
  },
  successCallback,
  failCallback,
  unauthorizedCallback,
  forbiddenCallback,
  notfoundCallback
)

export default service
export const jqFormSubmitError = submitFormError(forbiddenCallback, notfoundCallback, failCallback)

const installer = {
  vm: {},
  install(Vue) {
    Vue.use(VueAxios, service)
  }
}

export { installer as VueAxios, service as axios }
