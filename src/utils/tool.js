
/**
* @description 注册回车事件
*/
export const handleEnter = (hc) => {
  document.onkeydown = (e) => { // 按下回车提交
    var event = e || window.event
    if (event.key && event.key === 'Enter') {
      hc()
    } else if (event.which === 13) {
      hc()
    }
  }
}
export function errorScroll() {
  const isError = document.getElementsByClassName('has-error')
  if (isError[0].querySelector('.ant-upload')) {
    isError[0].querySelector('input').parentNode.scrollIntoView({
      behavior: 'smooth', // 平滑过渡
      block: 'center' // 上边框与视窗顶部平齐。默认值
    })
  } else {
    isError[0].scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  }
}

// 判断内容是否是base64图片
export function isBase64Image(str) {
  if (typeof str !== 'string') {
    return false
  }
  const base64ImagePattern = /^data:image\/(jpeg|jpg|png|gif|bmp|webp);base64,[A-Za-z0-9+/]+={0,2}$/
  return base64ImagePattern.test(str)
}
