// with polyfills
import 'core-js/stable'
// 在有了babel-runtime的情况下，考虑不需要再引入regenerator-runtime
// import 'regenerator-runtime/runtime'
import store from '@/store'
import storage from 'store'

import '@/core/lazy_use'
import '@/utils/filter'

import {
  ACCESS_TOKEN,
  APP_LANGUAGE
} from '@/store/mutation-types'
import { printANSI } from '@/utils/screenLog'

export default function Initializer() {
  printANSI() // 请自行移除该行.  please remove this line
  store.commit('SET_TOKEN', storage.get(ACCESS_TOKEN))

  store.dispatch('setLang', storage.get(APP_LANGUAGE, 'zh_CN'))
  // last step
}
Initializer()
