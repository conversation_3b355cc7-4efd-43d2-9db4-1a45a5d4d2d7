import Vue from 'vue'

// base library
import {
  Modal,
  message,
  notification
} from 'ant-design-vue'
import Viser from 'viser-vue'

// ext library
// import VueCropper from 'vue-cropper'
import Dialog from '@/components/Dialog'
import Layer from '@/components/Layer'
// import PermissionHelper from '@/utils/helper/permission'
import JsEncrypt from 'jsencrypt'
import request from '@/utils/request'
import './directives/action'
import './directives/safety'
import Validator from './directives/validator.js'

Vue.prototype.$confirm = Modal.confirm
Vue.prototype.$message = message
Vue.prototype.$notification = notification
Vue.prototype.$info = Modal.info
Vue.prototype.$success = Modal.success
Vue.prototype.$error = Modal.error
Vue.prototype.$warning = Modal.warning
Vue.prototype.$request = request
Vue.prototype.$jsEncrypt = function(data) {
  const encrypt = new JsEncrypt()
  encrypt.setPublicKey(
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCR/BAOEBX2tnWSbax0HPMoGAyWfU18+QurAs3D\nESvsNOX0EdkUBQAej7qNW8rML4I8I/eEiBemqWnsbJs0Wvy9bikt0GuPxuikfNZDGUNAKTkvJc81\n2altzbO/DfunYJEoH6FQMyk1JykcKfCyE33LbReiTlMpj2EHvtMFvZRp5wIDAQAB'
  )
  return encrypt.encrypt(data)
}

Vue.use(Viser)
Vue.use(Dialog) // this.$dialog func
Vue.use(Layer)
// Vue.use(PermissionHelper)
// Vue.use(VueCropper) // 图片裁剪
Vue.use(Validator) // 校验

process.env.NODE_ENV !== 'production' && console.warn('[antd-pro] NOTICE: Antd use lazy-load.')
