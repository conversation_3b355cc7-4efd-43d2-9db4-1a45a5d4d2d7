const directive = {
  inserted(el, binding, vnode) {
    const { value } = binding
    const wrapperDiv = document.createElement('div')
    wrapperDiv.setAttribute('v-validator', true)
    const msgDiv = document.createElement('div')
    msgDiv.setAttribute('style', 'margin-top: 2px')
    el.parentNode && el.parentNode.insertBefore(wrapperDiv, el)
    wrapperDiv.append(el)
    msgDiv.setAttribute('class', 'ant-form-explain leading-none text-gray-400')
    el.parentNode.insertBefore(msgDiv, el.nextSibling)

    if (value && typeof value === 'string') {
    } else if (value && typeof value === 'object' && value.value) {
      if (value.type === 'Info') {
      } else if (value.type === 'Error') {
        wrapperDiv.setAttribute('class', 'has-error')
      }
    }
  },
  update(el, binding, vnode) {
    const { value } = binding
    if (el.parentNode && el.parentNode.getAttribute('v-validator')) {
      const wrapperDiv = el.parentNode
      const msgDiv = el.nextSibling
      if (value && typeof value === 'string') {
        msgDiv.innerHTML = value
        wrapperDiv.removeAttribute('class')
      } else if (value && typeof value === 'object' && value.value) {
        msgDiv.innerHTML = value.value
        if (value.type === 'Info') {
          wrapperDiv.removeAttribute('class')
        } else if (value.type === 'Error') {
          wrapperDiv.setAttribute('class', 'has-error')
        }
      } else {
        wrapperDiv.removeAttribute('class')
        msgDiv.innerHTML = ''
      }
    }
  }
}

const install = function(Vue) {
  Vue.directive('validator', directive)
}

export default {
  install
}
