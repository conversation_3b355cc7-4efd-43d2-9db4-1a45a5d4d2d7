import Vue from 'vue'
import store from '@/store'
import safetyModal from '@/components/SafetyModal'

/**
 * safety 安全验证弹窗指令
 * 指令用法：
 *  - 在需要弹出安全验证弹窗的按钮上添加， v-safety="url" , 如下：
 *    <a-button v-safety="[url, ()=> {fun()}]">删除用户</a-button>
 *  url: 需要权限控制的url
 *  fun: 安全验证的回调执行的回调函数
 */
const safety = Vue.directive('safety', {
  bind: function(el, binding, vnode) {
    el.onclick = function() {
      const safetyMap = store.state.permission.safetyMap
      const [url, func] = binding.value
      if (!safetyMap[url]) {
        func.call(vnode.context)
      } else {
        safetyModal
          .showSafetyModal(safetyMap[url])
          .then(() => {
            func.call(vnode.context)
          })
          .catch(err => {
            console.log(err)
          })
      }
    }
  },
  componentUpdated: function(el, binding, vnode) {
    el.onclick = function() {
      const safetyMap = store.state.permission.safetyMap
      const [url, func] = binding.value
      if (!safetyMap[url]) {
        func.call(vnode.context)
      } else {
        safetyModal
          .showSafetyModal(safetyMap[url])
          .then(() => {
            func.call(vnode.context)
          })
          .catch(err => {
            console.log(err)
          })
      }
    }
  }
})

export default safety
