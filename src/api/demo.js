// import request from '@/utils/request'
import service from '@/utils/request'
import { isIE9 } from '@/utils/util'
import qs from 'qs'
const request = service('/app')

function post(url, data, config) {
  return request({
    url: url,
    method: 'post',
    data,
    ...config
  })
}
export default {

  // 查询投诉详情
  getDetail(data) {
    return post(
      '/wechatComplaint/info',
      data,
      {
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        }
      }
    )
  },
  // 下载
  getDownload(params) {
    if (isIE9()) {
      window.parent.open(request.defaults.baseURL + '/wechatComplaint/download?' + qs.stringify(params, { indices: false }))
      return Promise.resolve()
    }
    return request.get('/wechatComplaint/download', {
      params,
      responseType: 'arraybuffer'
    })
  }
}
