/**
 * v-hasPermission 操作权限处理
 * 2024-05-27 16:33:41 yangning.liu
 */

import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    const allPermission = '*:*:*'
    const permissionIds = store.getters && store.getters.permissionIds

    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissionIds.some(permission => {
        return allPermission === permission || permissionFlag.includes(permission)
      })
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}
