import './public-path'
import Vue from 'vue'

// @ts-ignore
import App from './App.vue'

import '@/core/init'

// local module config
import './style/index.less'

import '@/router/permission'
import store from './store'
import router from './router'
import VueClipboard from 'vue-clipboard2'

import Render, { config, material } from '@yeepay/lowcode-renderer'
import { material as Antd } from '@yeepay/antd-materials'
import { createService } from '@/utils/http'
import { getEnv } from '@/utils/index'
import '@/assets/styles/index.less'

import '@yeepay/lowcode-renderer/dist/styles/index.less'
import '@yeepay/antd-materials/dist/styles/index.less'
import emoji from '@/utils/emoji'
import VueRouter from 'vue-router'
import directive from '@/directive'
Vue.use(directive)
Vue.use(VueClipboard)

Vue.use(Render) // 渲染器 vueuse
material.install([Antd].flat())// antd 物料集
config({
  projectCode: 'mp-electronic-government', // 自动注入项目枚举
  // 埋点信息
  tracker: {
    sdkUrl: 'https://img.yeepay.com/fe-resources/webfunny-probe/boss-pc/webEvent.js',
    // pointName 需要与编辑器上点位名称相对应
    points: [{ pointName: '低代码_页面浏览', pointId: '110' }, { pointName: '低代码_点击事件', pointId: '109' }],
    sharedPointField: {}
  },
  env: getEnv(), // 项目环境统一配置，比如自行封装个getEnv()函数
  request: createService('/', ['SUCCESS', '000000', '00000', '200', 200])
})
Vue.directive('emoji', emoji)

Vue.filter('formatMoney', function (value: any) {
  if (!value) return '0.00'
  const s = value.toFixed(2)
  const l = s
    .split('.')[0]
    .split('')
    .reverse()
  const r = s.split('.')[1]
  let t = ''
  for (let i = 0; i < l.length; i++) {
    t += l[i] + ((i + 1) % 3 === 0 && i + 1 !== l.length ? ',' : '')
  }
  return (
    t
      .split('')
      .reverse()
      .join('') +
    '.' +
    r
  )
})

Vue.config.productionTip = false
let routerInstance: VueRouter | undefined = router
let instance: any = null

function render({ container, actions }: { container?: any, actions?:any } = {}) {
  console.log('main.js (48)', container)

  instance = new Vue({
    router: routerInstance,
    store,
    render: h => h(App)
  }).$mount((container as any) ? container.querySelector('#mpElectronicGovernment') : '#mpElectronicGovernment')
}

if (!(window as any).__POWERED_BY_QIANKUN__) {
  console.log('-------------------------------------')
  render()
}
// 测试全局变量污染
console.log('window.a', (window as any).a)
export async function bootstrap() {
  console.log('vue app bootstraped')
}

export async function mount(props: any) {
  console.log('props from main framework', props)

  // router.beforeEach((to, from, next) => {
  //   props.actions.setGlobalState({
  //     path: to.path,
  //     name: to.name
  //   })
  //   next()
  // })
  Vue.prototype.parentProps = props
  render(props)
}

export async function unmount() {
  instance.$destroy()
  instance = null
  routerInstance = undefined
}
