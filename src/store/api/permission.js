import getModuleRequest from '@/utils/getModuleRequest'
const { post, get } = getModuleRequest('/app')

export function getModules() {
  return post('/menu/query-first-menus')
}

export function getMenus(params) {
  return get('menu/query-sub-menus', params)
}
export function getSubject(params) {
  return get('apis/v2/account/subject', params)
}
export function querySubMenus(params) {
  return get('menu/query-top-menu', params)
}
