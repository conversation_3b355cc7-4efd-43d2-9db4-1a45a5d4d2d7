import { login, getInfo, logout } from '@/store/api/login'
import { getToken, setToken } from '@/utils/auth'
import { queryInfoForYop } from '@/store/api/permissionForYop'
import { queryInfoForMerchantAccess } from '@/store/api/permissionForMerchantAccess'

const user = {
  state: {
    token: getToken(),
    user: {},
    roles: [],
    loginInfo: {},
    merchantInfo: {},
    menus: []
  },

  mutations: {
    SET_MERCHANT_INFO: (state, payload) => {
      state.merchantInfo = payload
      const userId = payload.merchantNo
      const userTag = payload.enterpriseName
      window.localStorage.wmUserInfo = JSON.stringify({ userId, userTag, projectVersion: '1.0.1', env: 'pro', merchantInfo: payload })
    },
    SET_LOGIN_INFO: (state, payload) => {
      state.loginInfo = payload
      /* 前端监控: 注入用户标识 */
    },
    SET_TOKEN: (state, token) => {
      state.token = token
      setToken(token)
    },
    SET_USER: (state, user) => {
      state.user = user
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_MENUS: (state, menus) => {
      state.menus = menus
    }
  },

  actions: {
    QueryMerchantInfo({ commit }) {
      return new Promise((resolve, reject) => {
        // merchantInfoApi
        //   .queryInfo()
        //   .then(response => {
        //     commit('SET_MERCHANT_INFO', response.data)
        //     resolve()
        //   })
        //   .catch(error => {
        //     reject(error)
        //   })
        queryInfoForMerchantAccess()
          .then(response => {
            if (response.data.status !== 'success') {
              reject(response.data.errMsg)
              return
            }
            window.merchantInfo = response.data.data
            commit('SET_MERCHANT_INFO', response.data.data)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    QueryMerchantInfoForYop({ commit }) {
      return new Promise((resolve, reject) => {
        queryInfoForYop()
          .then(response => {
            commit('SET_MERCHANT_INFO', response.data)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    QueryMerchantInfoForMerchantAccess({ commit }) {
      return new Promise((resolve, reject) => {
        queryInfoForMerchantAccess()
          .then(response => {
            if (response.data.status !== 'success') {
              reject(response.data.errMsg)
              return
            }
            commit('SET_MERCHANT_INFO', response.data.data)
            resolve()
          })
      })
    },
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo)
          .then(response => {
            const result = response.result
            commit('SET_TOKEN', result.token)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            setUserInfo(res.data, commit)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 登出
    Logout({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(res => {
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
          .finally(() => {
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
          })
      })
    }
  }
}

export const setUserInfo = (res, commit) => {
  // 如果没有任何权限，则赋予一个默认的权限，避免请求死循环
  if (res.roles.length === 0) {
    commit('SET_ROLES', ['ROLE_SYSTEM_DEFAULT'])
  } else {
    commit('SET_ROLES', res.roles)
  }
  commit('SET_USER', res.user)
}

export default user
