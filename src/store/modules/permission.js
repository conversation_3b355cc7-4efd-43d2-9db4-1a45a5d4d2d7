import { constantRouterMap } from '@/router'
import { getSafetyMap } from '@/store/api/permissionForYop'
const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: [],
    defaultRouter: '',
    modules: [],
    menus: [],
    safetyMap: {},
    permissionIds: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    },
    SET_DEFAULT_ROUTER: (state, router) => {
      state.defaultRouter = router
    },
    SET_MODULES: (state, modules) => {
      state.modules = modules
    },
    SET_SAFETYMAP: (state, safetyMap) => {
      state.safetyMap = safetyMap
    },
    SET_MENUS: (state, menus) => {
      state.menus = menus
    },
    SET_PERMISSIONIDS: (state, permissionIds) => {
      state.permissionIds = permissionIds
    }
  },
  actions: {
    GenerateRoutes({ commit }, asyncRouter) {
      commit('SET_ROUTERS', asyncRouter)
      if (asyncRouter[0].children && asyncRouter[0].children.length > 0) {
        commit('SET_DEFAULT_ROUTER', asyncRouter[0].children[0].path)
      } else {
        if (!asyncRouter[0].hidden) {
          commit('SET_DEFAULT_ROUTER', asyncRouter[0].path)
        }
      }
    },
    GetSafetyMap({ commit }) {
      getSafetyMap().then(res => {
        commit('SET_SAFETYMAP', res.data)
      })
    },
    SetDefaultRouter({ commit }, router) {
      commit('SET_DEFAULT_ROUTER', router)
    },
    GenerateModules({ commit }, modules) {
      commit('SET_MODULES', modules)
    },
    GenerateMenus({ commit }, menus) {
      commit('SET_MENUS', menus)
    },
    GeneratePermissionIds({ commit }, permissionIds) {
      commit('SET_PERMISSIONIDS', permissionIds)
    }
  }
}

export default permission
