
/**
 * 路由
 * @type { *[] }
 */
export const customRouterMap = [
  // 交易中心
  {
    path: '/tradeCenter/tradeQuery',
    name: 'MpTradeQuery',
    meta: {
      menuId: 94254,
      tabName: '交易查询'
    },
    component: () => import('@/views/tradeCenter/tradeQuery/index')
  },
  // 交易汇总
  {
    path: '/tradeCenter/tradeSummary',
    name: 'MpTradeSummary',
    meta: {
      menuId: 94254,
      tabName: '交易汇总'
    },
    component: () => import('@/views/tradeCenter/tradeSummary/index')
  },
  // 交易中心-订单详情
  {
    path: '/tradeCenter/tradeQuery/detail',
    name: 'MpOrderDetail',
    meta: {
      menuId: 491237,
      tabName: '订单详情',
      topParentName: '数字政务',
      topParentModule: '/mp-electronic-government'
    },
    component: () => import('@/views/tradeCenter/tradeQuery/detail')
  },
  // 单笔退款查询
  {
    path: '/tradeCenter/singleRefundQuery',
    name: 'MpSingleRefundQuery',
    meta: {
      menuId: 1111,
      tabName: '单笔退款查询'
    },
    component: () => import('@/views/tradeCenter/singleRefundQuery/index')
  },
  // 批量退款查询
  {
    path: '/tradeCenter/batchRefundQuery',
    name: 'MpBatchRefundQuery',
    meta: {
      menuId: 1111,
      tabName: '批量退款查询'
    },
    component: () => import('@/views/tradeCenter/batchRefundQuery/index')
  },
  // 批量退款
  {
    path: '/tradeCenter/batchRefund',
    name: 'MpBatchRefund',
    meta: {
      menuId: 1111,
      tabName: '批量退款'
    },
    component: () => import('@/views/tradeCenter/batchRefund/index')
  },
  // 单笔退款复核
  {
    path: '/tradeCenter/singleRefundReview',
    name: 'MpSingleRefundReview',
    meta: {
      menuId: 1111,
      tabName: '单笔退款复核'
    },
    component: () => import('@/views/tradeCenter/singleRefundReview/index')
  },
  // 单笔退款复核
  {
    path: '/tradeCenter/batchRefundReview',
    name: 'MpBatchRefundReview',
    meta: {
      menuId: 1111,
      tabName: '批量退款复核'
    },
    component: () => import('@/views/tradeCenter/batchRefundReview/index')
  },
  // 交易中心-生成付款链接
  {
    path: '/tradeCenter/paymentLink',
    name: 'MpPaymentLink',
    meta: {
      menuId: 491231,
      tabName: '生成付款链接'
    },
    component: () => import('@/views/tradeCenter/paymentLink/index')
  },
  // 分账管理
  {
    path: '/separateManage/separateRulerConfig',
    name: 'MpSeparateRulerConfig',
    meta: {
      menuId: 491231,
      tabName: '分账规则配置'
    },
    component: () => import('@/views/separateManage/separateRulerConfig')
  },
  // 分账管理-分账规则详情(四级菜单)
  {
    path: '/separateManage/detail',
    name: 'MpSeparateRulerConfigDetail',
    meta: {
      menuId: 491232,
      tabName: '分账规则详情',
      topParentName: '数字政务',
      topParentModule: '/mp-electronic-government'
    },
    component: () => import('@/views/separateManage/separateRulerConfig/detail')
  },
  // 分账管理-分账汇总查询
  {
    path: '/separateManage/separateSummaryQuery',
    name: 'MpSeparateSummaryQuery',
    meta: {
      menuId: 491231,
      tabName: '分账汇总查询'
    },
    component: () => import('@/views/separateManage/separateSummaryQuery')
  },
  // 分账管理-分账明细查询
  {
    path: '/separateManage/separateDetailQuery',
    name: 'MpSeparateDetailQuery',
    meta: {
      menuId: 23712,
      tabName: '分账明细查询'
    },
    component: () => import('@/views/separateManage/separateDetailQuery')
  },
  // 分账管理-发起方账汇总查询
  {
    path: '/separateManage/initiateSummaryQuery',
    name: 'MpInitiateSummaryQuery',
    meta: {
      menuId: 491231,
      tabName: '发起方账汇总查询'
    },
    component: () => import('@/views/separateManage/initiateSummaryQuery')
  },
  // 分账管理-发起方账明细查询
  {
    path: '/separateManage/initiateDetailQuery',
    name: 'MpInitiateDetailQuery',
    meta: {
      menuId: 23712,
      tabName: '发起方账明细查询'
    },
    component: () => import('@/views/separateManage/initiateDetailQuery')
  },
  // 分账管理-接收分账汇总查询
  {
    path: '/separateManage/receiveSummaryQuery',
    name: 'MpReceiveSummaryQuery',
    meta: {
      menuId: 491231,
      tabName: '接收分账汇总查询'
    },
    component: () => import('@/views/separateManage/receiveSummaryQuery')
  },
  // 分账管理-接收分账明细查询
  {
    path: '/separateManage/receiveDetailQuery',
    name: 'MpSeparateDetailQuery',
    meta: {
      menuId: 23712,
      tabName: '接收分账明细查询'
    },
    component: () => import('@/views/separateManage/receiveDetailQuery')
  },
  // 票据中心
  {
    path: '/billCenter/billQuery',
    name: 'MpBillQuery',
    meta: {
      menuId: 787861,
      tabName: '票据查询'
    },
    component: () => import('@/views/billCenter/billQuery/index')
  },
  // 票据中心-申请开票
  {
    path: '/billCenter/applyBill',
    name: 'MpApplyBill',
    meta: {
      menuId: 786161,
      tabName: '申请开票'
    },
    component: () => import('@/views/billCenter/applyBill/index')
  },
  // 票据中心-票据号段查询
  {
    path: '/billCenter/billSegmentQuery',
    name: 'MpBillSegmentQuery',
    meta: {
      menuId: 786161,
      tabName: '票据号段查询'
    },
    component: () => import('@/views/billCenter/billSegmentQuery/index')
  },
  // 票据中心-票据冲红准备
  {
    path: '/billCenter/preparatioRedRush',
    name: 'MpPreparatioRedRush',
    meta: {
      menuId: 491231,
      tabName: '票据冲红准备'
    },
    component: () => import('@/views/billCenter/preparatioRedRush/index')
  },
  // 单位缴款
  {
    path: '/govPayment/applyPayment',
    name: 'MpApplyPayment',
    meta: {
      menuId: 491231,
      tabName: '单位缴款'
    },
    component: () => import('@/views/govPayment/applyPayment/index')
  },
  // 单位缴款-缴款记录
  {
    path: '/govPayment/paymentRecord',
    name: 'MpPaymentRecord',
    meta: {
      menuId: 491231,
      tabName: '缴款记录'
    },
    component: () => import('@/views/govPayment/paymentRecord/index')
  },
  // 单位缴款-收款账号管理
  {
    path: '/govPayment/paymentAccount',
    name: 'MpPaymentAccount',
    meta: {
      menuId: 491231,
      tabName: '收款账号管理'
    },
    component: () => import('@/views/govPayment/paymentAccount/index')
  },
  // 单位缴款-考试转账
  {
    path: '/govPayment/examTransfers',
    name: 'MpExamTransfers',
    meta: {
      menuId: 491231,
      tabName: '考试转账'
    },
    component: () => import('@/views/govPayment/examTransfers/index')
  },
  // 单位缴款-财政结算数据查询
  {
    path: '/govPayment/financialSettlement',
    name: 'MpFinancialSettlementQuery',
    meta: {
      menuId: 491231,
      tabName: '财政结算数据查询'
    },
    component: () => import('@/views/govPayment/financialSettlement/index')
  }
]
