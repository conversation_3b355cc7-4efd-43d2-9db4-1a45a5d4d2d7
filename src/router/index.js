import Vue from 'vue'
import Router from 'vue-router'
import store from '@/store'
import { setDocumentTitle } from '@/utils/domUtil'
import { i18nRender } from '@/locales'
import { customRouterMap } from './customRouter'
// hack router push callback
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/403',
    name: '403',
    meta: {
      tabName: '403'
    },
    component: () => import(/* webpackChunkName: "fail" */ '@/components/Exception/403')
  },

  {
    path: '/404',
    name: '404',
    meta: {
      tabName: '404'
    },
    component: () => import(/* webpackChunkName: "fail" */ '@/components/Exception/404')
  },
  {
    path: '/500',
    name: '500',
    meta: {
      tabName: '500'
    },
    component: () => import(/* webpackChunkName: "fail" */ '@/components/Exception/500')
  }
]

Vue.use(Router)

// hack ie9 history.replaceState not support
window.history.replaceState = window.history.replaceState || function() {}

const router = new Router({
  mode: 'hash',
  routes: constantRouterMap
})

router.addRoutes(customRouterMap)

// 路由钩子
router.beforeEach((to, from, next) => {
  const menus = store.getters && store.getters.menus
  console.log('menus', menus)
  to.meta && typeof to.meta.tabName !== 'undefined' && setDocumentTitle(`${i18nRender(to.meta.tabName)}`)
  if (Vue.prototype.parentProps) {
    Vue.prototype.parentProps.actions.setGlobalState({
      path: to.path,
      // name: to.name,
      tabName: to.meta ? to.meta.tabName : '标签',
      query: to.query,
      meta: to.meta
    })
  }

  if (to.path === '/403' || to.path === '/404' || to.path === '/500') {
    next()
    return
  }

  next()
})

export default router
