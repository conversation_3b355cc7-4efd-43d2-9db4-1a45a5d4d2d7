import Cookie from 'js-cookie'
import { getQueryObject } from '@/utils/util'

const TokenKey = 'SHRIOSESSIONID'
let urlToken = ''

export function getToken() {
  return urlToken || Cookie.get(TokenKey)
}

/**
 * method set token from url.
 * @param {String} key Token key.
 */
function setTokenFromUrl(key) {
  // url has token ?
  urlToken = getQueryObject(window.location.href)[key]
  if (urlToken) {
    const hashIndex = urlToken.indexOf('#')
    if (hashIndex !== -1) {
      urlToken = urlToken.slice(0, hashIndex)
    }
    Cookie.set(TokenKey, urlToken)
    window.history.replaceState(null, null, window.location.pathname + window.location.hash)
  }
}

setTokenFromUrl('token') // set token
