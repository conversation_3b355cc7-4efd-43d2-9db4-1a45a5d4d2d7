<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <keep-alive :include="loadedRouteNames">
        <router-view v-if="!refresh" />
      </keep-alive>
    </div>
  </a-config-provider>
</template>

<script>
import { setDocumentTitle } from '@/utils/domUtil'
import { i18nRender } from '@/locales'
import store from '@/store'
export default {
  data () {
    return {
      loadedRouteNames: [],
      refresh: false
    }
  },
  computed: {
    locale () {
      // 只是为了切换语言时，更新标题
      const { title } = this.$route.meta
      title && (setDocumentTitle(`${i18nRender(title)}`))

      return this.$i18n.getLocaleMessage(this.$store.getters.lang).antLocale
    }
  },

  mounted() {
    if (window.__POWERED_BY_QIANKUN__) {
      this.parentProps.onGlobalStateChange(state => {
        if (state['mp-electronic-government'].refresh) {
          this.refresh = true
          setTimeout(() => {
            this.refresh = false
          }, 200)
        }
        const { childRoute, routerIds } = state['mp-electronic-government']

        // 动态加载路由
        // TODO:
        // 1. 动态加载路由，需要在子项目中，调用 this.$router.addRoutes(loadedRoutes)
        // const hasPermissionRoutes = generateRouter(routerIds)

        store.dispatch('GenerateMenus', routerIds)
        store.dispatch('GeneratePermissionIds', state['permissionIds'])

        // hash 模式需要去掉路由前面的 #/
        console.log('childRoute', childRoute)
        const loadedRoutes = childRoute.map(item => this.$router.resolve(item.slice(2)))
        console.log('loadedRoutes', loadedRoutes)
        const loadedRouteNames = loadedRoutes.map(item => item.route.name)
        console.log('loadedRouteNames', loadedRouteNames)
        this.loadedRouteNames = loadedRouteNames
      }, true)
    }
  }
}
</script>
