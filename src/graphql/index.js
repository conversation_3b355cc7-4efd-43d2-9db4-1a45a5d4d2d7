import { GraphQLClient } from '@yeepay/client-utils'
import VueApollo from 'vue-apollo'
import request from '@/utils/request'

export const apolloClient = new GraphQLClient({
  gqlServer: 'http://localhost:5000/graphql',
  requestForRest: request
})

// Provider 保存了可以在接下来被所有子组件使用的 Apollo 客户端实例。
export const apolloProvider = new VueApollo({
  defaultClient: apolloClient
})

require('./inject')

export default apolloClient.gql
