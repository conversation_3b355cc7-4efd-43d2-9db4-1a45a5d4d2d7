import moment from 'moment'
export default {
  data() {
    return {
      width: '',
      isResponseList: [
        {
          label: '是',
          value: 'true'
        },
        {
          label: '否',
          value: 'false'
        }
      ]
    }
  },
  mounted() {
    this.width = document.documentElement.clientWidth
    window.addEventListener('resize', this.getWidth)
  },
  methods: {
    getWidth() {
      this.width = document.documentElement.clientWidth
    },
    changeSelectDay() {
      const time = moment().format('YYYY-MM-DD')
      const start = time
      const end = moment(time)
        .add(-29, 'd')
        .format('YYYY-MM-DD')
      this.computeTime = [end, start]
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.getWidth)
  }
}
