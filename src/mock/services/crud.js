import Mock from 'mockjs2'
import { builder, getQueryParameters } from '../util'

const list = (options) => {
  const body = getQueryParameters(options)
  console.log('mock: body', body)

  const { page, size } = body
  const data = []

  for (let i = (page - 1) * size; i < page * size; i++) {
    data.push({
      key: i,
      createTime: Mock.mock('@datetime'),
      trxTime: Mock.mock('@datetime'),
      trxFlowNo: Mock.mock('@guid'),
      trxCode: Mock.mock('@cword(3)'),
      fee: Mock.mock({
        'number|1-100': 100
      }).number,
      income: Mock.mock({
        'number|1-100.1': 1
      }).number,
      expenditure: Mock.mock({
        'number|1-100.1': 1
      }).number,
      postBalance: Mock.mock({
        'number|1-100.1': 1
      }).number,
      remark: Mock.mock('@csentence'),
      accountType: Mock.mock('@cword(2)')
    })
  }

  return builder({
    records: data,
    total: 100
  }, '成功', 200, { 'Custom-Header': Mock.mock('@guid') })
}

Mock.mock(/\/api\/capitalflow\/query/, 'get', list)
