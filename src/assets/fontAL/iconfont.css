@font-face {
  font-family: "iconfont"; /* Project id 1389909 */
  src: url('iconfont.woff2?t=1719473669181') format('woff2'),
       url('iconfont.woff?t=1719473669181') format('woff'),
       url('iconfont.ttf?t=1719473669181') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconruzhangfangguanli_icon:before {
  content: "\e7e7";
}

.iconzhuanzhang:before {
  content: "\e608";
}

.icontixian:before {
  content: "\e607";
}

.iconchongzhi:before {
  content: "\e606";
}

.iconqiyefukuan1:before {
  content: "\e605";
}

.iconqiyefukuan:before {
  content: "\e604";
}

.icongerenfukuan:before {
  content: "\e602";
}

.iconshamenhangkong_MF:before {
  content: "\e636";
}

.icondonghang:before {
  content: "\e722";
}

.iconshanghaihangkong:before {
  content: "\e725";
}

.iconnanfanghangkong_CZ:before {
  content: "\e779";
}

.iconjiaoyiliang_icon:before {
  content: "\e7df";
}

.iconjidujiangli_icon:before {
  content: "\e7e0";
}

.iconyinhangkaguanli:before {
  content: "\e7a0";
}

.icondailishangguanli:before {
  content: "\e79c";
}

.iconfenrunguanli:before {
  content: "\e79a";
}

.iconzhongduanguanli:before {
  content: "\e79b";
}

.icona-icon_duizhangguanliqudaoban:before {
  content: "\e798";
}

.iconfenrun_icon:before {
  content: "\e7d5";
}

.iconxiajiguanli:before {
  content: "\e79f";
}

.iconduisiruzhangfang:before {
  content: "\e791";
}

.iconduigongruzhangfang:before {
  content: "\e790";
}

.iconfenzhang:before {
  content: "\e78e";
}

.iconshoudan:before {
  content: "\e78f";
}

.iconicon_taipai:before {
  content: "\e6df";
}

.iconicon_wangdian:before {
  content: "\e6e0";
}

.iconicon_shanghuguanli:before {
  content: "\e6d2";
}

.iconicon_jiesuanguanli:before {
  content: "\e6d3";
}

.iconicon_xiajishanghuguanli1:before {
  content: "\e6d4";
}

.iconxiaoshoufangguanli:before {
  content: "\e6b0";
}

.iconwodeguanli:before {
  content: "\e6ad";
}

.icona-icon_accountmanagement:before {
  content: "\e6a6";
}

.iconyibaozhifuLOGOyuanwenjian:before {
  content: "\e76e";
}

.iconicon_duizhangzhongxin:before {
  content: "\e69e";
}

.iconicon_fenzhangguanli:before {
  content: "\e69f";
}

.iconicon_mendianguanli:before {
  content: "\e6a0";
}

.iconicon_pos:before {
  content: "\e6a1";
}

.iconposji:before {
  content: "\e799";
}

.iconfanhuidingbu:before {
  content: "\e6b4";
}

.iconzhifuzhong:before {
  content: "\e6a9";
}

.iconchenggong:before {
  content: "\e6aa";
}

.iconshibai:before {
  content: "\e6ab";
}

.icona-dianzan_kuaibeifen8:before {
  content: "\e696";
}

.icona-dianzan_kuaibeifen9:before {
  content: "\e697";
}

.icona-bianzu3beifen:before {
  content: "\e698";
}

.iconkaifataojian-dinggao1:before {
  content: "\e695";
}

.icona-bianzu62:before {
  content: "\e693";
}

.icondianhua:before {
  content: "\e68d";
}

.iconyinhangka:before {
  content: "\e68c";
}

.iconqiyezhanghu:before {
  content: "\e68b";
}

.iconbangzhu:before {
  content: "\e601";
}

.iconxiaoxi:before {
  content: "\e675";
}

.iconliebiao_:before {
  content: "\e674";
}

.icona-bianzu23:before {
  content: "\e673";
}

.iconshengjibeifen:before {
  content: "\e672";
}

.iconjuxingbeifen35:before {
  content: "\e671";
}

.iconyingwen:before {
  content: "\e66f";
}

.iconzhongwen:before {
  content: "\e670";
}

.iconkaifazhezhongxin:before {
  content: "\e66b";
}

.iconshanghuguanli:before {
  content: "\e66c";
}

.iconfapiaoguanli:before {
  content: "\e66d";
}

.iconyuangongguanli:before {
  content: "\e66e";
}

.iconwendabeifen5:before {
  content: "\e66a";
}

.iconzhiding:before {
  content: "\e667";
}

.iconkefu:before {
  content: "\e668";
}

.iconfankuibeifen:before {
  content: "\e669";
}

.iconfrown-o:before {
  content: "\e666";
}

.iconyingyong:before {
  content: "\e664";
}

.icondeveloper:before {
  content: "\e651";
}

.iconxiaoxihezi-kongbeifen:before {
  content: "\e658";
}

.iconlogo-tu:before {
  content: "\e659";
}

.iconRectangleCopy:before {
  content: "\e638";
}

.iconRectangleCopy1:before {
  content: "\e639";
}

