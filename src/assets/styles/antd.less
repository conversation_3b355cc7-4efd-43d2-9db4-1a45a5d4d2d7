@import '~ant-design-vue/es/style/themes/default.less';

// alert 无边框
.ant-alert {
  border: 0;
}

// 菜单 hover 无下划线
.ant-menu-horizontal > .ant-menu-item:hover:not(.ant-menu-item-selected), .ant-menu-horizontal > .ant-menu-item-active:not(.ant-menu-item-selected) {
  border-bottom: 2px solid transparent !important;
}

// 菜单文字左侧对齐
.ant-menu-item.close-fit, .ant-menu-submenu.close-fit>div, .ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
  padding-left: 24px !important;
}


// 穿梭框内容不可选中
.ant-transfer-list-content {
  user-select: none;
}

// 解决 firefox position 定位存在问题
.ant-form-item-children {
  display: block;
}

@media (max-width: @screen-xs) {
  .ant-pro-basicLayout-content {
    margin: 0;
  }
}
.ant-table {
  &-tbody > tr:hover:not(.ant-table-expanded-row) > td,.ant-table-row-hover,.ant-table-row-hover>td{
    background: rgba(0, 0, 0, 0.02) !important;
  }
}
.ant-select-dropdown-menu-item:hover:not(.ant-select-dropdown-menu-item-disabled) {
  background: rgba(0, 0, 0, 0.02) !important;
}
.ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
  background: rgba(0, 0, 0, 0.02) !important;
}