@import '~ant-design-vue/es/style/themes/default.less';
@import './icon-font.less';

html,
body,
#app,
#root {
  height: 100%;
}

* {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
}

p {
  margin: 0;
}

.primary-color {
  color: @primary-color;
}
.colorWeak {
  filter: invert(80%);
}

.ant-layout.layout-basic {
  height: 100vh;
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

// 数据列表 样式
.table-alert {
  margin-bottom: 16px;
}
// 数据列表 操作
.table-operator {
  margin-bottom: 18px;

  button {
    margin-right: 8px;
  }
}
// 数据列表 搜索条件
.table-page-search-wrapper {
  .ant-form-inline {
    .ant-form-item {
      display: flex;
      margin-bottom: 24px;
      margin-right: 0;

      .ant-form-item-control-wrapper {
        flex: 1 1;
        display: inline-block;
        vertical-align: middle;
      }

      > .ant-form-item-label {
        line-height: 32px;
        padding-right: 8px;
        width: auto;
      }
      .ant-form-item-control {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .table-page-search-submitButtons {
    display: block;
    margin-bottom: 24px;
    white-space: nowrap;
  }
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.page-container {
  padding: 22px;
}

input::placeholder,
textarea::placeholder {
  color: #000000;
  font-size: 14px;
  opacity: 0.55;
}

.ant-select-selection__placeholder,
.ant-select-search__field__placeholder {
  color: #000000;
  font-size: 14px;
  opacity: 0.15;
}

.ant-cascader-picker-arrow {
  opacity: 0.4;
}
.ant-btn[disabled]:hover {
  color: rgba(0,0,0,.25) !important;
  border-color: #d9d9d9 !important;
}
.ant-pro-global-header-logo img {
  height: 28px !important;
}
.ant-input-clear-icon {
  color: #BFBFBF !important;
}
.ant-calendar-picker-clear {
  color: #BFBFBF !important;
}
.yee-page-title-wrapper .page-title-content .title {
  font-size: 16px !important;
}

// 设置描述组件的标题
.ant-descriptions-title {
  font-size: 14px !important;
}