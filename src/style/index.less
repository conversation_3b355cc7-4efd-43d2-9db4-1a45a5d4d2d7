.action {
  color: @primary-color;
  cursor: pointer;
  // display: inline-block;
}
.date-wrap {
  position: relative;
  .date {
    display: flex;
    height: 32px;
    margin-bottom: 10px;
  }
  .date-list {
    width: 250px;
    position: absolute;
    left: 0;
    bottom: -25px;
    display: flex;
    .date-item {
      font-size: 14px;
      // font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #4e4e4e;
      line-height: 20px;
      margin-right: 23px;
      cursor: pointer;
    }
    .date-item-active {
      color: @primary-color;
    }
  }
}
.form-item {
  width: 100%;
  height: 32px;
  display: flex;
  .label {
    line-height: 32px;
    display: inline-block;
    width: 82px;
  }
  .content {
    display: inline-block;
    flex: 1;
    min-width: 180px;
  }
}
.result-wrap {
  background: rgba(241, 241, 241, 0.5);
  padding: 16px 0;
  max-width: 600px;
  margin: 0 auto !important;
  .result-item {
    margin-bottom: 16px;
    &:last-child {
      margin-bottom: 0;
    }
    span {
      display: inline-block;
      text-align: left;
    }
    .label {
      width: 100px;
      color: rgba(0, 0, 0, 0.85);
    }
    .num {
      width: 100px;
    }
    .result-item-money {
      padding-left: 20px;
      width: 170px;
    }
    .result-item-right {
      width: 160px;
    }
  }
}
.preview-font {
  color: @primary-color;
  cursor: pointer;
}
.yee-page-title-wrapper {
  .page-title-content {
    height: 54px;
    padding-top: 0 !important;
    margin-top: 2px;
  }
  .ant-divider-horizontal {
    margin: 0 !important;
    background-color: #EBEBEB !important;
  }
  .operation-content {
    align-items: center;
  }
}
.ant-message-success .anticon {
  color: #52c41a !important;
}