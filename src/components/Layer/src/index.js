/**
 * @file Layer
 * <AUTHOR>
 * @date 2021/2/4
 * @description 弹层组件: 'modal', 'drawer'
 */

import Drawer from 'ant-design-vue/es/drawer'
import Modal from 'ant-design-vue/es/modal'
import Button from 'ant-design-vue/es/button'
import { getComponentFromProp, mergeProps } from '../../_util/util'
import { mapState } from 'vuex'
import { openMask, closeMask } from '@/utils/iframe'

const props = {
  type: {
    type: String,
    default: 'modal',
    validator: function(value) {
      return ['modal', 'drawer'].indexOf(value) !== -1
    }
  },
  maskClosable: {
    type: Boolean,
    default: false
  },
  okText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  }
}

export default {
  name: 'Layer',
  props: { ...Drawer.props, ...Modal.props, ...props },
  data() {
    return {
      footStyle: {
        position: 'absolute',
        right: 0,
        bottom: 0,
        width: '100%',
        borderTop: '1px solid #e9e9e9',
        padding: '10px 16px',
        background: '#fff',
        textAlign: 'right',
        zIndex: 1
      }
    }
  },
  computed: {
    ...mapState({
      isMobile: state => state.app.isMobile
    })
  },
  methods: {
    handleCancel(e) {
      this.$emit('cancel', e)
      this.$emit('change', false)
    },
    handleOk(e) {
      this.$emit('ok', e)
    },
    renderFooter() {
      var h = this.$createElement
      var okType = this.okType
      var confirmLoading = this.confirmLoading

      var cancelBtnProps = mergeProps({ on: { click: this.handleCancel } }, this.cancelButtonProps || {}, { style: { marginRight: '8px' } })
      var okBtnProps = mergeProps({
        on: { click: this.handleOk },
        props: {
          type: okType,
          loading: confirmLoading
        }
      }, this.okButtonProps || {})
      return h('div', [h(
        Button,
        cancelBtnProps,
        [getComponentFromProp(this, 'cancelText')]
      ), h(
        Button,
        okBtnProps,
        [getComponentFromProp(this, 'okText')]
      )])
    }
  },
  render(h) {
    const { type, visible } = this.$props

    // 老后台 mask 唤起与关闭
    if (visible) {
      openMask()
    } else {
      closeMask()
    }

    const slots = Object.keys(this.$slots)
      .reduce((arr, key) => arr.concat(this.$slots[key]), [])
      // 手动更正 context
      .map(vnode => {
        vnode.context = this._self
        return vnode
      })

    return type === 'drawer' ? h(Drawer, {
      on: this.$listeners,
      attrs: this.$attrs,
      props: {
        ...this.$props,
        width: this.isMobile ? '100%' : this.$props.width,
        bodyStyle: { paddingBottom: '80px', maxWidth: '100vw' }
      }
    }, [
      ...slots,
      h('div', {
        style: this.footStyle
      }, [
        this.renderFooter()
      ])
    ]) : h(Modal, {
      on: this.$listeners,
      attrs: this.$attrs,
      props: this.$props
    }, slots)
  }
}
