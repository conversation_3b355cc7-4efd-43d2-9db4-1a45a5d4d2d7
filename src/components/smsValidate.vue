<template>
  <a-modal
    title="修改分账信息"
    :visible="visible"
    :confirm-loading="confirmLoading"
    :maskClosable="false"
    ok-text="下一步"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="content">
      <p>
        <span class="sms-key">用户名:</span>
        <span class="sms-value">{{ loginName }}</span>
      </p>
      <p>
        <span class="sms-key">已绑定手机:</span>
        <span class="sms-value">{{ bindPhone }}</span>
      </p>
      <p class="code-box">
        <a-form-model
          ref="form"
          :model="form"
          style="width: 65%;"
        >
          <a-form-model-item
            label="短信验证码:"
            prop="verificationCode"
            :rules="{
              required: true,
              message: '请填写短信验证码',
              trigger: 'blur'
            }"
            :labelCol="{span: 8}"
            :wrapperCol="{span: 15}"
            :colon="false"
          >
            <a-input
              v-model="form.verificationCode"
              class="code"
              placeholder="请输入验证码"
            />
          </a-form-model-item>
        </a-form-model>
        <a-button v-if="showGetCode" class="btn" :loading="getCodeLoading" @click="sendSms">获取验证码</a-button>
        <a-button v-else class="btn" disabled>{{ num }}s后重新获取</a-button>
      </p>
      <span v-if="!showGetCode" class="tip">短信已发送至{{ bindPhone }}，请注意查收</span>
    </div>
  </a-modal>
</template>
<script>
import Api from '@/api/validate'

export default {
  name: 'SmsValidate',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loginName: {
      type: String,
      default: ''
    },
    bindPhone: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      confirmLoading: false,
      getCodeLoading: false,
      time: null,
      num: 60,
      showGetCode: true,
      form: {
        verificationCode: ''
      }
    }
  },
  methods: {
    sendSms() {
      this.getCodeLoading = true
      Api.getMobileVerifyCode({
        loginName: this.loginName,
        businessType: 'MODIFY_DIVIDE_INFO'
      })
        .then((response) => {
          const res = response.data
          if (res.status === 'success') {
            const that = this
            this.showGetCode = false
            this.time = setInterval(function() {
              that.num = that.num - 1
              if (that.num < 0) {
                clearInterval(that.time)
                that.time = null
                that.num = 60
                that.showGetCode = true
              }
            }, 1000)
          } else {
            this.$message.error(res.errMsg)
          }
        })
        .finally(() => {
          this.getCodeLoading = false
        })
    },
    handleOk() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.validate()
        } else {
          return false
        }
      })
    },
    validate() {
      this.confirmLoading = true
      Api.checkMobileCode({
        loginName: this.loginName,
        businessType: 'MODIFY_DIVIDE_INFO',
        verificationCode: this.form.verificationCode
      })
        .then((response) => {
          const res = response.data
          if (res.status === 'success') {
            this.reset()
            this.$emit('validateOK', res.data.token)
          } else {
            this.$message.error(res.errMsg)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    handleCancel() {
      this.reset()
      this.$emit('update:visible', false)
    },
    reset() {
      this.$refs.form.resetFields()
    }
  }
}
</script>
<style scoped>
.content {
  padding: 0px 16px 0px 46px;
}
p {
  margin: 8px 0 16px;
}
.sms-key {
  display: inline-block;
  width: 90px;
  text-align: right;
  padding-right: 8px;
  font-weight: 500;
}
.sms-value {
  display: inline-block;
}
.code-box {
  display: flex;
  margin-bottom: 4px;
}
.tip {
  margin-left: 90px;
  color: rgba(0, 0, 0, 0.45);
}
.ant-form-item {
  margin-bottom: 0;
}
::v-deep .ant-form-item-label {
  display: inline-block;
  width: 90px;
  padding-right: 8px;
  font-weight: 500;
}
::v-deep .ant-form-item-label > label {
  color: rgba(0, 0, 0, 0.65);
}
.btn {
  margin-top: 4px;
}
</style>
