<template>
  <a-modal title="安全验证" wrapClassName="safetyModal" :width="700" :visible="visible" @cancel="handleCancel">
    <div class="form-wrap">
      <a-form-model :model="formInline" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-form-model-item label="短信验证码">
          <div class="input">
            <a-input
              v-model="code"
              :maxLength="6"
              :style="{ width: '200px', marginRight: '10px', marginBottom: '10px' }"
            />
            <a-button v-if="showNum" disabled>重新获取（{{ num }}）s</a-button>
            <a-button v-else-if="hasClick" type="primary" @click="getCode">重新获取</a-button>
            <a-button v-else type="primary" @click="getCode">获取验证码</a-button>
            <div v-if="showTelephone" class="input-message">短信验证码已发送至{{ showTelephone }}</div>
          </div>
        </a-form-model-item>
      </a-form-model>
    </div>
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :disabled="!code" @click="confirm">确认</a-button>
    </template>
  </a-modal>
</template>
<script>
import { createService } from '@/utils/http'
const request = createService('/yop-developer-center')
export default {
  data() {
    return {
      visible: false,
      showNum: false,
      hasClick: false,
      showTelephone: '',
      num: 60,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      formInline: {
        user: '',
        password: ''
      },
      options: {},
      type: 1,
      code: ''
    }
  },
  methods: {
    showModal(options) {
      this.visible = true
      this.showNum = false
      this.hasClick = false
      this.num = 60
      this.code = ''
      this.time = null
      this.timer = null
      this.showTelephone = ''
      this.options = options
      return new Promise((resolve, reject) => {
        this.resolve = resolve
        this.reject = reject
      })
    },
    getCode() {
      request({
        url: '/apis/v1/captcha/send',
        method: 'post',
        data: {
          bizType: this.options.bizType
        }
      })
        .then(res => {
          if (res.message !== 'SUCCESS') {
            this.$message.error(res.message)
          }
          if (res.message === 'SUCCESS') {
            this.showNum = true
            this.hasClick = true
            if (res.data && res.data.receiver) {
              this.showTelephone = res.data.receiver
            }
            const that = this
            this.time = setInterval(function() {
              that.num = that.num - 1
              if (that.num < 0) {
                clearInterval(that.time)
                that.time = null
                that.num = 60
                that.showNum = false
                that.showTelephone = false
              }
            }, 1000)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    confirm() {
      if (!this.code) {
        this.$message.error('请输入验证码')
        return
      }
      if (this.timer) return
      this.timer = setTimeout(() => {
        request({
          url: '/apis/v1/captcha/validate',
          method: 'post',
          data: {
            bizType: this.options.bizType,
            captcha: this.code
          }
        })
          .then(res => {
            if (res.message === 'SUCCESS') {
              this.resolve()
              this.visible = false
              this.$destroy()
              if (this.time) clearInterval(this.time)
            }
          })
          .catch(err => {
            console.log(err)
          })
          .finally(() => {
            this.timer = null
          })
      }, 200)
    },
    handleCancel(e) {
      this.visible = false
      this.reject()
      if (this.time) clearInterval(this.time)
      if (this.timer) clearTimeout(this.timer)
      this.$destroy()
    }
  }
}
</script>
<style lang="less">
.safetyModal {
  .form-wrap {
    height: 180px;
    padding-top: 50px;
    .input {
      display: flex;
      position: relative;
      flex-wrap: wrap;
      white-space: nowrap;
      .input-message {
        position: absolute;
        left: 0;
        bottom: -30px;
      }
    }
  }
}
</style>
