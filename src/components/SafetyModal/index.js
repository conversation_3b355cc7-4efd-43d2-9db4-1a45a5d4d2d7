import safetyModal from './index.vue'
import Vue from 'vue'
// 创建Vue对象
const SafetyModal = Vue.extend(safetyModal)
let instance = ''
let instanceDom = ''
export default {
  showSafetyModal(options = {}) {
    if (instanceDom) {
      document.body.removeChild(instanceDom)
    }
    instanceDom = document.createElement('div')
    instance = new SafetyModal({
      el: instanceDom
    })
    document.body.appendChild(instanceDom)
    return instance.showModal(options)
  }
}
