<template>
  <div class="map-wrap">
    <div class="map-tools">
      <a-input
        id="tipInput"
        placeholder="请输入内容"
        style="width: 220px;"
      />
      <a-button icon="reload" @click="searchReset"/>
    </div>
    <div id="container" :style="{width: '100%', height: `${height}px`}" class="map-container"></div>
    <div v-if="loading" class="map-wrap-mark">
      <a-icon type="loading"/>
      <span>定位中...</span>
    </div>
  </div>
</template>
<script>
import AMapLoader from '@amap/amap-jsapi-loader'

window._AMapSecurityConfig = {
  securityJsCode: '1db27bc831dabef85d44a9d218fd30ab'
}

export default {
  name: 'GDMap',
  props: {
    height: {
      type: Number || String,
      default: '300'
    },
    lng: {
      type: Number || String,
      default: ''
    },
    lat: {
      type: Number || String,
      default: ''
    },
    defaultAddress: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      map: null,
      AMap: null,
      address: '',
      showSearchResult: false,
      poiList: [],
      infoWindow: null,
      geolocation: null,
      geocoder: null,
      loading: false
    }
  },
  computed: {
    lngLat() {
      return this.lat && this.lng ? [Number(this.lng), Number(this.lat)] : [116.457195, 39.919723]
    },
    hasDefaultAddress() {
      return this.defaultAddress && this.lng && this.lat
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      AMapLoader.reset()
      AMapLoader.load({
        key: '97e889873a54392b06c22ff39623387e',
        version: '1.4.15',
        plugins: [
          'AMap.ToolBar',
          'AMap.Scale',
          'AMap.Geolocation',
          'AMap.PlaceSearch',
          'AMap.Autocomplete',
          'AMap.Geocoder',
          'AMap.CitySearch',
          'AMap.ToolBar'
        ],
        resizeEnable: true
      }).then((AMap) => {
        this.AMap = AMap
        this.map = new this.AMap.Map('container', {
          resizeEnable: true,
          zoom: 15,
          center: this.lngLat
        })
        const toolBar = new this.AMap.ToolBar({
          liteStyle: true
        })
        this.map.addControl(toolBar)
        this.mapSearchInit()
        this.geocoder = new this.AMap.Geocoder()
        if (this.hasDefaultAddress) {
          this.address = this.defaultAddress
          this.showLocation(this.lngLat[0], this.lngLat[1], this.defaultAddress)
        }
        this.map.on('click', (e) => {
          this.showLocation(e.lnglat.getLng(), e.lnglat.getLat())
        })
      }).catch(e => {
        console.log(e)
      })
    },
    // 获取当前定位
    getCurrentLocation() {
      this.geolocation = new this.AMap.Geolocation({
        timeout: 5000,
        enableHighAccuracy: true,
        zoomToAccuracy: true
      })
      this.loading = true
      this.geolocation.getCurrentPosition((status, result) => {
        if (status === 'complete') {
          this.onComplete(result)
        } else {
          this.$message.error({
            content: '自动定位失败，请在地图中手动选择',
            duration: 3
          })
          this.showLocation(this.lngLat[0], this.lngLat[1])
          this.loading = false
        }
      })
    },
    // 解析定位结果
    onComplete(data) {
      const lnglat = data.position
      this.showLocation(lnglat.lng, lnglat.lat)
      this.loading = false
    },
    // 搜索
    mapSearchInit() {
      const autoCompleteComponent = new this.AMap.Autocomplete({
        input: 'tipInput'
      })
      autoCompleteComponent.on('select', (e) => {
        this.showLocation(e.poi.location.lng, e.poi.location.lat, e.poi.name)
      })
    },
    searchReset() {
      if (this.hasDefaultAddress) {
        this.showLocation(Number(this.lng), Number(this.lat), this.defaultAddress)
      }
    },
    // 显示标记
    showLocation(lng, lat, name) {
      const marker = new this.AMap.Marker({
        position: new this.AMap.LngLat(lng, lat),
        draggable: true,
        cursor: 'move'
      })
      this.map.clearMap()
      this.map.add(marker)
      this.showInfoWindow(marker)
      this.dragMarker(marker, name)
      marker.on('dragstart', () => {
        this.infoWindow.close()
      })
      marker.on('dragend', () => {
        this.dragMarker(marker)
      })
    },
    // 自定义信息窗体
    showInfoWindow(marker) {
      this.infoWindow = new this.AMap.InfoWindow({
        isCustom: true,
        content: `<div style="padding: 8px;background-color: white; border-radius: 5px;border: 1px solid #d9d9d9;"> 地址：${this.address}</div>`,
        closeWhenClickMap: true,
        zIndex: 999,
        offset: new this.AMap.Pixel(0, -28)
      })
      this.infoWindow.open(this.map, marker.getPosition())
    },
    dragMarker(marker, name) {
      const coordinate = marker.getPosition()

      this.geocoder.getAddress(coordinate, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          const regeocode = result.regeocode
          const addressComponent = regeocode.addressComponent
          this.address = name || addressComponent.building || addressComponent.neighborhood || this.getFormattedAddress(regeocode.formattedAddress, addressComponent)
          const mapInfo = {
            province: addressComponent.adcode.substring(0, 2) + '0000',
            city: addressComponent.adcode.substring(0, 4) + '00',
            district: addressComponent.adcode,
            provinceName: addressComponent.province,
            cityName: addressComponent.city || addressComponent.province,
            districtName: addressComponent.district,
            address: this.address,
            longitude: coordinate.lng,
            latitude: coordinate.lat
          }
          this.showInfoWindow(marker)
          this.$emit('getMapInfo', mapInfo)
        } else {
          this.$emit('getMapInfo', {
            province: '',
            city: '',
            district: '',
            provinceName: '',
            cityName: '',
            districtName: '',
            address: '',
            longitude: coordinate.lng,
            latitude: coordinate.lat
          })
        }
      })
      marker.setMap(this.map)
      setTimeout(() => {
        this.map.setCenter([coordinate.lng, coordinate.lat])
      }, 100)
    },
    getFormattedAddress(address, cityInfo) {
      let _address = address
      const { province, city, district } = cityInfo
      _address = this.getFilterAddress(province, _address)
      _address = this.getFilterAddress(city, _address)
      _address = this.getFilterAddress(district, _address)
      return _address
    },
    getFilterAddress(str, address) {
      let _address = address
      if (str && str.length) {
        const _str = _address.substring(0, str.length)
        if (_str === str) {
          _address = _address.substring(str.length, _address.length)
        }
      }
      return _address
    }
  }
}
</script>
<style>
.amap-info {
  width: 220px !important;
}
</style>
<style scoped>
.map-wrap {
  position: relative;
  border: 1px solid #d9d9d9
}

.map-wrap-mark {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .3);
  color: #ffffff;
  z-index: 1000;
}

.map-wrap-mark span {
  margin-left: 5px;
}

.map-tools {
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 1;
}

.map-tools .ant-btn {
  margin-left: 8px;
  font-size: 12px;
}
</style>
