<!--
 * formItem的type 可根据实际页面拓展，保持通用性即可
 * width优先级: 没设置时，默认是labelCol,wrapperCol的span,formItems
 *
 -->
<script>
export default {
  name: 'FormModel',
  props: {
    // 表单数据对象
    form: {
      type: Object,
      default: () => ({})
    },
    // 表单字段配置数组
    formItems: {
      type: Array,
      default: () => []
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    // 表单布局配置
    labelCol: {
      type: Object,
      default: () => ({ span: 4 })
    },
    wrapperCol: {
      type: Object,
      default: () => ({ span: 12 })
    },
    // 按钮配置
    buttons: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    // 处理宽度配置
    getItemWidth() {
      return (item) => {
        if (!item.width) return '100%' // 366px
        if (typeof item.width === 'number') {
          return `${item.width}px`
        }
        return item.width
      }
    }
  },
  methods: {
    // 处理表单提交
    handleSubmit() {
      this.$emit('submit', this.form)
    },
    // 处理按钮点击
    handleButtonClick(button) {
      this.$emit('button-click', button, this.form)
    },
    // 更新表单字段值
    updateField(key, value) {
      this.$emit('field-change', key, value)
    },
    // 表单验证方法
    validateField(callback) {
      return this.$refs.ruleForm.validate(callback)
    },
    // 重置表单验证
    resetFields() {
      return this.$refs.ruleForm.resetFields()
    },
    // 清除验证结果
    clearValidate(props) {
      return this.$refs.ruleForm.clearValidate(props)
    }
  }
}
</script>
<!-- @ts-nocheck -->
<template>
  <a-form-model
    ref="ruleForm"
    :model="form"
    :rules="rules"
    :label-col="labelCol"
    :wrapper-col="wrapperCol"
    class="custom-form-model">
    <!-- 动态渲染表单项 -->
    <a-form-model-item
      v-for="item in formItems"
      :key="item.key"
      :label="item.label"
      :prop="item.key"
    >
      <!-- 优先使用插槽，如果有对应的插槽则使用插槽内容 -->
      <slot
        v-if="$scopedSlots[item.key]"
        :name="item.key"
        :item="item"
        :value="form[item.key]"
        :disabled="item.disabled"
        :updateValue="(value) => updateField(item.key, value)"
      />

      <!-- 如果没有插槽，则使用默认的表单控件 -->
      <template v-else>
        <!-- Input 输入框 -->
        <a-input
          v-if="item.type === 'input'"
          :value="form[item.key]"
          :disabled="item.disabled"
          v-bind="item.props"
          :style="{ width: getItemWidth(item) }"
          :max-length="item.max"
          @input="updateField(item.key, $event.target.value)"
        >
          <span v-if="item.suffix" slot="suffix">{{ item.suffix }}</span>
        </a-input>

        <!-- Textarea 文本域 -->
        <div v-else-if="item.type === 'textarea'" class="textarea">
          <a-input
            :value="form[item.key]"
            :disabled="item.disabled"
            type="textarea"
            v-bind="item.props"
            :style="{ width: getItemWidth(item) }"
            :max-length="item.max"
            @input="updateField(item.key, $event.target.value)"
          />
          <div
            v-if="item.showCount !== false"
            class="textarea-count"
            :style="{
              width: getItemWidth(item)
            }"
          >
            {{ (form[item.key] || '').length }}/{{ item.max || 100 }}
          </div>
        </div>

        <!-- InputNumber 数字输入框 -->
        <div v-else-if="item.type === 'inputNumber'" class="input-number-wrapper">
          <a-input-number
            :min="item.min"
            :precision="item.precision||2"
            :value="form[item.key]"
            :disabled="item.disabled"
            v-bind="item.props"
            :style="{ width: item.suffix ? `calc(${getItemWidth(item)} - 40px)` : getItemWidth(item) }"
            @change="updateField(item.key, $event)"
          />
          <span v-if="item.suffix" class="input-number-suffix">{{ item.suffix }}</span>
        </div>

        <!-- Select 选择器 -->
        <!-- <a-select
          v-else-if="item.type === 'select'"
          :value="form[item.key]"
          :disabled="item.disabled"
          @change="updateField(item.key, $event)"
          v-bind="item.props"
          :style="{ width: getItemWidth(item) }"
        >
          <a-select-option
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-select-option>
        </a-select> -->

        <!-- InputNumber 数字输入框 (备用) -->
        <!-- <div v-else-if="item.type === 'inputNumber'" class="input-number-wrapper">
          <a-input-number
            :min="item.min"
            :max="item.max"
            :precision="item.precision||2"
            :value="form[item.key]"
            :disabled="item.disabled"
            v-bind="item.props"
            :style="{ width: item.suffix ? `calc(${getItemWidth(item)} - 40px)` : getItemWidth(item) }"
            @change="updateField(item.key, $event)"
          />
          <span v-if="item.suffix" class="input-number-suffix">{{ item.suffix }}</span>
        </div> -->

        <!-- Date Picker 日期选择器 -->
        <!-- <a-date-picker
          v-else-if="item.type === 'date-picker'"
          :value="form[item.key]"
          @change="updateField(item.key, $event)"
          v-bind="item.props"
          :style="{ width: getItemWidth(item) || '100%' }"
        /> -->

        <!-- Switch 开关 -->
        <!-- <a-switch
          v-else-if="item.type === 'switch'"
          :checked="form[item.key]"
          @change="updateField(item.key, $event)"
          v-bind="item.props"
        /> -->

        <!-- Checkbox Group 多选框组 -->
        <!-- <a-checkbox-group
          v-else-if="item.type === 'checkbox-group'"
          :value="form[item.key]"
          @change="updateField(item.key, $event)"
          v-bind="item.props"
        >
          <a-checkbox
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-checkbox>
        </a-checkbox-group> -->

        <!-- Radio Group 单选框组 -->
        <!-- <a-radio-group
          v-else-if="item.type === 'radio-group'"
          :value="form[item.key]"
          @change="updateField(item.key, $event.target.value)"
          v-bind="item.props"
        >
          <a-radio
            v-for="option in item.options"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </a-radio>
        </a-radio-group> -->

        <!-- 默认为 Input -->
        <a-input
          v-else
          :value="form[item.key]"
          :disabled="item.disabled"
          v-bind="item.props"
          :style="{ width: getItemWidth(item) }"
          @input="updateField(item.key, $event.target.value)"
        />
      </template>
    </a-form-model-item>

    <!-- 按钮区域 -->
    <a-form-model-item
      v-if="buttons.length > 0"
      :wrapper-col="{ offset: labelCol.span }"
    >
      <a-button
        v-for="(button, index) in buttons"
        :key="index"
        :type="button.type || 'default'"
        :style="index > 0 ? 'margin-left: 10px;' : ''"
        @click="handleButtonClick(button)"
      >
        {{ button.text }}
      </a-button>
    </a-form-model-item>
  </a-form-model>
</template>

<style lang="less" scoped>
.custom-form-model {
  ::v-deep .ant-input[disabled] {
    // color: #999;
  }
  .ant-form-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &:nth-last-child(2){
      margin-bottom: 0;
    }
  }

  .ant-form-item-label {
    flex: none;
    width: var(--label-width, 120px);
    text-align: right;
    padding-right: 8px;
    line-height: 32px;
  }

  .ant-form-item-control {
    flex: 1;
  }

  ::v-deep .ant-btn{
    padding: 0 10px;
  }

  ::v-deep .ant-input-affix-wrapper .ant-input-suffix {
    background-color: #eaeaea;
    padding: 5px 7px;
    border-radius: 0px 2px 2px 0px;
    color: #666;
    right: 1px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.24);
  }

  .textarea-count {
    line-height: 30px;
    position: absolute;
    bottom: 2px;
    right: 6px;
    text-align: right;
    color: #666;
    font-size: 14px;
    margin-top: 4px;
  }

  .input-number-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    ::v-deep .ant-input-number {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;

      .ant-input-number-handler-wrap {
        display: none;
      }
    }
    .input-number-suffix {
      background-color: #eaeaea;
      padding: 5px 7px;
      border-radius: 0px 2px 2px 0px;
      color: #666;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.24);
      border: 1px solid #d9d9d9;
      border-left: none;
      height: 32px;
      line-height: 20px;
      display: flex;
      align-items: center;
      min-width: 40px;
      justify-content: center;
    }
  }
}

</style>
