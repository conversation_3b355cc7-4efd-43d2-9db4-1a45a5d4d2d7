<template>
  <div>
    <a-upload
      :accept="accept"
      action="/app/merchant/middle/netIn/fileUpload"
      :headers="{'interaction-type': 'ERROR_CODE', 'X-WX-Id': xwxId || ''}"
      :file-list="fileList"
      :before-upload="beforeUpload"
      @change="handleChange"
    >
      <a-button> <a-icon type="upload" /> 点击上传 </a-button>
    </a-upload>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    urlName: {
      type: String,
      default: ''
    },
    accept: {
      type: String,
      default: ''
    },
    maxSize: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      fileList: [],
      xwxId: window.localStorage.getItem('X-WX-Id')
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.showPicture()
        } else {
          this.fileList = []
        }
      },
      immediate: true
    }
  },
  methods: {
    // 上传回调
    handleChange(info) {
      // 判断登录失效接口会返回401，跳转登录界面
      if (typeof info.file.response === 'string' && info.file.response.indexOf('401') > -1) {
        const origin = window.location.origin
        window.location.href = `${origin}/auth/signin?redirectUrl=${origin}/mp-merchant-access/index.html#/`
      }
      if (info.file.status === 'done') {
        if (info.file.response.status === 'success') {
          const url = info.file.response.data ? info.file.response.data.fileUrl : ''
          this.$emit('input', url)
          this.$emit('clearValidate', this.urlName)
        } else {
          this.$message.error(info.file.response.errMsg)
        }
      }
      if (info.file.status) {
        let fileList = [...info.fileList]
        fileList = fileList.slice(-1)
        this.fileList = fileList
        // 上传后可点击展示
        fileList = fileList.map(file => {
          if (file.response) {
            file.url = file.response.data ? file.response.data.fileUrl : ''
          }
          return file
        })
      }
      if (info.file.status === 'removed') {
        this.$emit('input', '')
      }
    },
    // 回显文件
    showPicture() {
      // let name = this.value.split('.')[0]
      // const temp = name.split('/')
      // name = temp[temp.length - 1]
      const temp = this.value.split('.')
      const suffix = temp[temp.length - 1]
      !this.fileList.length && this.fileList.push({
        uid: '-1',
        name: 'file.' + suffix,
        status: 'done',
        url: this.value
      })
    },
    // 上传前限制大小
    beforeUpload(file) {
      const fileSize = file.size / 1024 / 1024
      if (fileSize > this.maxSize) {
        this.$message.error(`文件需${this.maxSize}M以内!`)
        return false
      }
      return true
    }
  }
}
</script>
