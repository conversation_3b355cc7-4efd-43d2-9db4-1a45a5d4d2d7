/**
 * components util
 */

import _slicedToArray from 'babel-runtime/helpers/slicedToArray'
import isPlainObject from 'lodash/isPlainObject'
import _extends from 'babel-runtime/helpers/extends'
import { getPropsData } from 'ant-design-vue/lib/_util/props-util'

/**
 * 清理空值，对象
 * @param children
 * @returns {*[]}
 */
export function filterEmpty(children = []) {
  return children.filter(c => c.tag || (c.text && c.text.trim() !== ''))
}

/**
 * 获取字符串长度，英文字符 长度1，中文字符长度2
 * @param {*} str
 */
export const getStrFullLength = (str = '') =>
  str.split('').reduce((pre, cur) => {
    const charCode = cur.charCodeAt(0)
    if (charCode >= 0 && charCode <= 128) {
      return pre + 1
    }
    return pre + 2
  }, 0)

/**
 * 截取字符串，根据 maxLength 截取后返回
 * @param {*} str
 * @param {*} maxLength
 */
export const cutStrByFullLength = (str = '', maxLength) => {
  let showLength = 0
  return str.split('').reduce((pre, cur) => {
    const charCode = cur.charCodeAt(0)
    if (charCode >= 0 && charCode <= 128) {
      showLength += 1
    } else {
      showLength += 2
    }
    if (showLength <= maxLength) {
      return pre + cur
    }
    return pre
  }, '')
}

export function mergeProps() {
  var args = [].slice.call(arguments, 0)
  var props = {}
  args.forEach(function() {
    var p = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {}
    var _iteratorNormalCompletion2 = true
    var _didIteratorError2 = false
    // eslint-disable-next-line no-undef-init
    var _iteratorError2 = undefined

    try {
      for (var _iterator2 = Object.entries(p)[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {
        var _ref3 = _step2.value

        var _ref4 = _slicedToArray(_ref3, 2)

        var k = _ref4[0]
        var v = _ref4[1]

        props[k] = props[k] || {}
        if (isPlainObject(v)) {
          _extends(props[k], v)
        } else {
          props[k] = v
        }
      }
    } catch (err) {
      _didIteratorError2 = true
      _iteratorError2 = err
    } finally {
      try {
        if (!_iteratorNormalCompletion2 && _iterator2['return']) {
          _iterator2['return']()
        }
      } finally {
        if (_didIteratorError2) {
          // eslint-disable-next-line no-unsafe-finally
          throw _iteratorError2
        }
      }
    }
  })
  return props
}

function getScopedSlots(ele) {
  return ele.data && ele.data.scopedSlots || {}
}

export function getComponentFromProp(instance, prop) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : instance
  var execute = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true

  if (instance.$createElement) {
    var h = instance.$createElement
    var temp = instance[prop]
    if (temp !== undefined) {
      return typeof temp === 'function' && execute ? temp(h, options) : temp
    }
    return instance.$scopedSlots[prop] && execute && instance.$scopedSlots[prop](options) || instance.$scopedSlots[prop] || instance.$slots[prop] || undefined
  } else {
    var _h = instance.context.$createElement
    var _temp = getPropsData(instance)[prop]
    if (_temp !== undefined) {
      return typeof _temp === 'function' && execute ? _temp(_h, options) : _temp
    }
    var slotScope = getScopedSlots(instance)[prop]
    if (slotScope !== undefined) {
      return typeof slotScope === 'function' && execute ? slotScope(_h, options) : slotScope
    }
    var slotsProp = []
    var componentOptions = instance.componentOptions || {};
    (componentOptions.children || []).forEach(function(child) {
      if (child.data && child.data.slot === prop) {
        if (child.data.attrs) {
          delete child.data.attrs.slot
        }
        if (child.tag === 'template') {
          slotsProp.push(child.children)
        } else {
          slotsProp.push(child)
        }
      }
    })
    return slotsProp.length ? slotsProp : undefined
  }
}
