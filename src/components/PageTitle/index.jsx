import PropTypes from 'ant-design-vue/es/_util/vue-types'
import { mapState } from 'vuex'
import './index.less'

const TitleProps = {
  title: PropTypes.string.def('')
}

const PageTitle = {
  name: 'PageTitle',
  props: TitleProps,
  created() {
    this.$watch('$route', () => {
      this.updateMetaTitle()
    }, {
      immediate: true
    })
  },
  computed: {
    ...mapState({
      isMobile: state => state.app.isMobile
    }),
    visible: function() {
      return !this.isMobile || (this.$slots.left || this.$slots.right)
    }
  },
  render() {
    const title = this.title || this.metaTitle
    return (
      this.visible && <div class="page-title-container">
        <div class="page-title-content">
          { !this.isMobile && <span class="title">{title}</span> }
          { this.$slots.left && <div class="left">
            { this.$slots.left }
          </div> }
          { this.$slots.right && <div class="right">
            { this.$slots.right }
          </div> }
        </div>
        <a-divider />
      </div>
    )
  },
  methods: {
    updateMetaTitle () {
      const routes = this.$route.matched.concat()
      const { hidden } = this.$route.meta
      if (routes.length >= 3 && hidden) {
        this.metaTitle = routes[routes.length - 1].meta.title
      } else {
        this.metaTitle = routes.pop().meta.title
      }
    }
  }
}

PageTitle.install = function (Vue) {
  Vue.component('page-title', PageTitle)
}

export default PageTitle
