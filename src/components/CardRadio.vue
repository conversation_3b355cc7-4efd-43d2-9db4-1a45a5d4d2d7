<template>
  <div class="card-radio-wrap flex justify-between">
    <div
      v-for="item in options"
      :key="item.value"
      :class="['option-item relative p-1', { selected: item.value === value }]"
      @click="onClick(item)"
    >
      <img v-if="item.value === value" class="absolute icon" :src="checkedIcon" />
      <div class="flex items-center">
        <img :src="item.value === value ? item.checkedIcon : item.uncheckIcon" />
        <div class="flex flex-col items-start justify-center px-2 text-left">
          <p>{{ item.label }}</p>
          <div class="desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkedIcon: require('../assets/img/checked.png')
    }
  },
  methods: {
    onClick(item) {
      this.$emit('input', item.value)
    }
  }
}
</script>

<style scoped>
.option-item {
  width: 210px;
  border-radius: 6px;
  border: 1px solid #efefef;
  padding: 13px 0;
  padding-left: 24px;
  cursor: pointer;
}
.option-item:hover,
.selected {
  background: rgba(243, 96, 70, 0.1);
  box-shadow: 0px 5px 10px 0px rgba(243, 96, 70, 0.3);
  border: 1px solid #f36046;
}
.option-item img {
  width: 32px;
  height: 32px;
}
.option-item p {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
  margin-bottom: 8px;
}
.option-item .desc {
  font-size: 12px;
  line-height: 16px;
  color: #666666;
}
.option-item .icon {
  height: 16px;
  width: 16px;
  top: 8px;
  right: 8px;
}
.ant-modal-root ::v-deep .ant-modal-body {
  padding-top: 8px;
  padding-bottom: 0px;
}
.card-radio-wrap {
  padding: 8px 32px 0;
  padding-bottom: 0;
  max-height: calc(80vh - 116px);
  overflow: auto;
}
</style>
