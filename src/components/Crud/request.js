import qs from 'qs'

let request = null

export function initData(url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get'
  })
}

export function download(url, params) {
  return request({
    url: url + '?' + qs.stringify(params, { indices: false }),
    method: 'get',
    responseType: 'blob'
  })
}

export function getCustomColumns(url, params = {}) {
  return request({
    url: url,
    method: 'get',
    params
  })
}

export function saveCustomColumns(url, params) {
  return request({
    url: url,
    method: 'post',
    data: params
  })
}

export function injectRequest(value) {
  request = value
}

export function getRequest() {
  return request
}
