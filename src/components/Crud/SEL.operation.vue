<template>
  <a-alert v-if="selectedRowKeys.length" type="info" class="sel-operation">
    <p slot="description">
      <span>已选择 {{ selectedRowKeys.length }} 项</span>
      <span>
        <slot></slot>
      </span>
      <a class="clear">
        <span @click="clear">取消选择</span>
      </a>
    </p>
  </a-alert>
</template>

<script>
export default {
  name: 'SELOperationVue',
  props: {
    selectedRowKeys: {
      type: Array,
      default: () => ([])
    }
  },
  methods: {
    clear() {
      this.$emit('update:selectedRowKeys', [])
    }
  }
}
</script>

<style lang="less">
.sel-operation {
  margin-bottom: 16px;

  p {
    margin-top: -4px;
  }

  .clear, .clear:hover {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 14px;
    cursor: pointer;
    color: @primary-color;
  }
}
</style>
