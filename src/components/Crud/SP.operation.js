import { Button } from 'ant-design-vue'
import AdaptField from './adapt-field/index'
import CRUD, { search } from '@/components/Crud/crud'
import rrOperation from '@/components/Crud/RR.operation'
import SearchItem from './adapt-field/item-field/main'

export default {
  name: 'SP.operation',
  components: { rrOperation },
  mixins: [search()],
  props: {
    fieldSet: {
      type: Object
    },
    moreConditions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      prefix: 'crud-search-panel',
      collapse: true,
      fields: [],
      moreFields: []
    }
  },
  created() {
    this.fields = this.adaptField(this.fieldSet)
    this.moreFields = this.adaptField(this.moreConditions)
    this.assembleQuery(this.fields)
    this.assembleQuery(this.moreFields)
  },
  watch: {
    fieldSet: {
      handler(fieldSet) {
        this.fields = new AdaptField([], {
          useAll: true,
          fieldSet
        }).fieldSets
      },
      deep: true
    },
    fields: {
      handler(newFields) {
        newFields.forEach(field => {
          this.fieldSet[field.key].value = field.value
          this.crud.query[field.key] = field.value
        })
      },
      deep: true
    },
    moreConditions: {
      handler(fieldSet) {
        this.moreFields = new AdaptField([], {
          useAll: true,
          fieldSet
        }).fieldSets
      },
      deep: true
    },
    moreFields: {
      handler(newFields) {
        newFields.forEach(field => {
          this.moreConditions[field.key].value = field.value
          this.crud.query[field.key] = field.value
        })
      },
      deep: true
    }
  },
  methods: {
    adaptField(fieldSet) {
      return new AdaptField([], {
        useAll: true,
        fieldSet
      }).fieldSets
    },
    assembleQuery(fields) {
      fields.map(_ => {
        this.crud.query[_.key] = _.value
        this.crud.defaultQuery[_.key] = _.value
      })
    },
    /**
     * method 自定义的 key, 主要用于 range 组件对应两个 key.
     * @param {Object} field.
     * @param {String | Array} value.
     */
    handleCustomKeys(field, value) {
      if (field.controlProps && field.controlProps.key) {
        const customKey = field.controlProps.key
        if (typeof customKey === 'string') {
          this.crud.query[customKey] = value
          return
        }

        if (
          Object.prototype.toString.call(customKey) === '[object Array]' &&
          customKey.length === 2 &&
          value.length === 2
        ) {
          this.crud.query[customKey[0]] = value[0]
          this.crud.query[customKey[1]] = value[1]
        }
      }
    },
    [CRUD.HOOK.beforeReset]() {
      this.fields.forEach(_ => {
        _.value = this.crud.defaultQuery[_.key]
      })
      this.moreFields.forEach(_ => {
        _.value = this.crud.defaultQuery[_.key]
      })
    },
    [CRUD.HOOK.beforeRefresh]() {
      this.fields.forEach(_ => {
        this.handleCustomKeys(_, _.value)
      })
      this.moreFields.forEach(_ => {
        this.handleCustomKeys(_, _.value)
      })
    },
    handleCollapse() {
      this.collapse = !this.collapse
    }
  },
  render(h) {
    return h(
      'div',
      {
        class: this.prefix
      },
      [
        h(
          'div',
          {
            class: 'content-wrapper'
          },
          [
            this.fields.map(field =>
              h(SearchItem, {
                props: {
                  ...field.controlProps,
                  _key: field.key,
                  type: field.type,
                  value: field.value,
                  label: field.label,
                  placeholder: field.placeholder
                },
                on: {
                  change: value => (field.value = value)
                }
              })
            ),
            !this.collapse && this.moreFields.map(field =>
              h(SearchItem, {
                props: {
                  ...field.controlProps,
                  _key: field.key,
                  type: field.type,
                  value: field.value,
                  label: field.label,
                  placeholder: field.placeholder
                },
                on: {
                  change: value => (field.value = value)
                }
              })
            ),
            h(
              'div',
              {
                class: 'action-wrapper'
              },
              [
                !!this.moreFields.length &&
                  h(
                    Button,
                    {
                      props: {
                        type: 'link',
                        size: 'small',
                        icon: this.collapse ? 'down' : 'up'
                      },
                      on: {
                        click: this.handleCollapse
                      }
                    },
                    this.collapse ? '展开' : '折叠'
                  ),
                h(rrOperation)
              ]
            )
          ]
        )
      ]
    )
  }
}
