import presetTypes, { controlType, PRESET_CONTROL_TYPES } from './preset-types'

export default class AdaptField {
  constructor(presetTypesToUse, option = {}) {
    presetTypesToUse = presetTypesToUse || []
    this.fieldSet = []
    this.controlTypeSet = {}
    if (option.useAll || !presetTypesToUse.length) {
      PRESET_CONTROL_TYPES.forEach(typeName => {
        this.controlTypeSet[typeName] = Object.assign({}, presetTypes[typeName])
      })
    }
    presetTypesToUse.forEach(type => {
      if (typeof type === 'string') {
        this.use(type)
      } else {
        this.inject(type)
      }
    })

    const fieldSet = option.fieldSet || {}
    for (const id in fieldSet) {
      if (fieldSet.hasOwnProperty(id)) {
        const field = fieldSet[id]
        const { value, type } = field
        const stateField = Object.assign({
          id,
          key: id
        }, field)
        let initialValue = type && this.controlTypeSet[type] && this.controlTypeSet[type].initialValue

        if (type === controlType.DATE_RANGE) {
          if (Object.prototype.toString.call(value) === '[object Object]') {
            stateField.value = initialValue
          }
        } else {
          const voidTypes = ['select', 'suggest']
          if ('value' in field && value === '' && voidTypes.includes(type)) {
          } else if ('value' in field) {
            initialValue = value
          }

          stateField.value = initialValue
        }

        this.fieldSet.push(stateField)
      }
    }
  }

  use(typeName) {
    this.controlTypeSet[typeName] = Object.assign({}, presetTypes[typeName])
  }

  inject(typeConfig = {}) {
    const key = typeConfig.name
    if (key) {
      const originType = this.controlTypeSet[key] || presetTypes[key]
      this.controlTypeSet[key] = Object.assign({}, originType, typeConfig)
    }
  }

  get fieldSets() {
    return this.fieldSet
  }
}
