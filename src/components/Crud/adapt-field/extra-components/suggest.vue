<template>
  <a-select
    show-search
    :placeholder="placeholder"
    :value="value"
    :not-found-content="null"
    :multiple="multiple"
    :filter-option="false"
    :default-active-first-option="false"
    :show-arrow="false"
    @search="remoteMethod"
    @change="change"
  >
    <a-select-option
      v-for="item in actualOptions"
      :key="item.value"
      :value="item.value">
      {{ item.label }}
    </a-select-option>
  </a-select>
</template>
<script>

export default {
  name: 'Suggest',
  props: {
    value: {
      type: [String, Array],
      default: () => ([])
    },
    multiple: {
      type: Boolean,
      default: false
    },
    fetcher: {
      type: Function,
      default: () => ({})
    },
    options: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '输入内容'
    }
  },

  data() {
    return {
      loading: false,
      actualOptions: []
    }
  },

  created() {
    this.actualOptions = JSON.parse(JSON.stringify(this.options))
  },

  methods: {
    remoteMethod(inputValue) {
      this.fetcher(inputValue).then(options => {
        this.actualOptions = options
      })
    },
    change(value) {
      this.$emit('change', value)
      this.remoteMethod(value)
    }
  }

}
</script>
