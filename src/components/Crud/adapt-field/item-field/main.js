import presetTypes, { PRESET_CONTROL_TYPES } from '../../adapt-field/preset-types'
import { Select } from 'ant-design-vue'
import moment from 'moment'

export default {
  name: 'searchItem',
  props: {
    _key: {
      type: String | Number,
      require: true
    },
    type: {
      validator: function(value) {
        return PRESET_CONTROL_TYPES.includes(value)
      },
      default: 'input'
    },
    label: {
      type: String,
      default: ''
    },
    value: {
      type: String | Array | Boolean
    },
    placeholder: {
      type: String
    },
    description: {
      type: String | Function
    },
    allowClear: {
      type: Boolean,
      default: false
    },
    disabledDate: {
      type: Function,
      default: () => void 0
    },
    defaultPickerValue: {
      type: Array,
      default: () => []
    },
    allOption: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array
    },
    fetcher: {
      type: Function
    },
    multiple: {
      type: Boolean,
      default: false
    },
    format: {
      type: String
    },
    valueFormat: {
      type: String
    },
    showTime: {
      type: Boolean | String,
      default: false
    }
  },
  data() {
    return {
      prefix: 'crud-search-item',
      Option: Select.Option
    }
  },
  mounted() {},
  computed: {
    controlTypeSet() {
      return Object.assign({}, presetTypes[this.type])
    },
    picker() {
      return this.setDefaultFormat()
    }
  },
  methods: {
    setValue(e) {
      let value = null
      const { type } = this
      const { valueFormat } = this.picker
      if (type.includes('picker')) {
        if (e instanceof Array) {
          value = e.map(_ => moment(_).format(valueFormat))
        } else if (typeof e === 'object') {
          value = moment(e).format(valueFormat)
        }
      } else {
        if (e instanceof Array) {
          value = e
        } else if (typeof e === 'object') {
          value = e.target.value
        } else {
          value = e
        }
      }
      this.$emit('change', value)
    },
    setDefaultFormat() {
      const { type } = this
      let valueFormat = null
      let format = null
      if (type === 'date-picker' || type === 'date-range-picker') {
        valueFormat = this.valueFormat || (this.showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD')
        format = this.format || (this.showTime ? 'YYYY 年 MM 月 DD 日 HH:mm:ss' : 'YYYY 年 MM 月 DD 日')
      } else if (type === 'month-picker' || type === 'month-range-picker') {
        valueFormat = this.valueFormat || 'YYYY-MM'
        format = this.format || 'YYYY 年 MM 月'
      }
      return {
        valueFormat,
        format
      }
    },
    handlePanel(isOpen) {
      /**
       * 月份范围选择后的交互, 不会自动关闭浮层, 那么我们需要一个通过 showTime 出现的[确定]按钮;
       * 但由于 showTime 带来的不仅仅有[确定]按钮, 还有[选择时间]按钮, 我们手动将其去掉
       */
      if (isOpen && this.type === 'month-range-picker') {
        setTimeout(() => {
          const extraOperations = document.querySelectorAll('.ant-calendar-time-picker-btn')
          extraOperations.forEach(_ => _.setAttribute('style', 'display: none'))
        })
      }
    }
  },
  render(h) {
    const { type, options } = this
    const props = {
      type: this.type,
      value: this.value,
      placeholder: this.placeholder,
      allowClear: this.allowClear,
      allOption: this.allOption,
      disabledDate: this.disabledDate,
      defaultPickerValue: this.defaultPickerValue
      // multiple: this.multiple,
    }

    const finalOptions = []
    if (type === 'select' || type === 'suggest') {
      if (props.allOption) {
        finalOptions.unshift({
          value: '0000',
          label: '全部'
        })
      }
      finalOptions.push(...options)
    }

    if (type.includes('picker')) {
      props.format = this.picker.format

      if (type.includes('range')) {
        if (type.includes('month')) {
          props.mode = ['month', 'month']
        }
        props.value = this.value ? this.value.map(_ => moment(_, this.picker.valueFormat)) : [null, null]
        props.defaultValue = this.value ? this.value.map(_ => moment(_, this.picker.valueFormat)) : [null, null]
        if (this.showTime) {
          setTimeout(() => {
            document.querySelector('#control_' + this._key).setAttribute('style', 'width: 570px')
          })
        }
      } else {
        props.value = this.value ? moment(this.value, this.picker.valueFormat) : null
        props.defaultValue = this.value ? moment(this.value, this.picker.valueFormat) : null
      }

      props.showTime = this.showTime

      if (type === 'month-range-picker') {
        /** 月份范围选择后的交互, 不会自动关闭浮层, 那么我们需要一个通过 showTime 出现的[确定]按钮 */
        props.showTime = true
      }
    }

    if (type === 'radio-group' || type === 'checkbox-group') {
      props.options = this.options
    }

    if (type === 'suggest') {
      props.fetcher = this.fetcher
    }

    return h(
      'div',
      {
        class: {
          [this.prefix]: true
        }
      },
      [
        h(
          'span',
          {
            class: 'label'
          },
          this.label
        ),
        h(
          'div',
          {
            attrs: {
              id: 'control_' + this._key
            },
            class: {
              control: true,
              'is-range': this.type.includes('range')
            }
          },
          [
            h(
              this.controlTypeSet.component,
              {
                props,
                on: {
                  input: this.setValue,
                  change: this.setValue,
                  /** 月份范围, 仅此事件生效 */
                  panelChange: this.setValue,
                  openChange: this.handlePanel
                }
              },
              [
                (type === 'select' || type === 'suggest') &&
                  finalOptions &&
                  finalOptions.map(option =>
                    h(
                      this.Option,
                      {
                        props: {
                          key: option.value,
                          value: option.value
                        }
                      },
                      option.label
                    )
                  )
              ]
            ),
            this.description &&
              h(
                'span',
                {
                  class: {
                    description: true
                  }
                },
                [typeof this.description === 'function' ? this.description() : this.description]
              )
          ]
        )
      ]
    )
  }
}
