import { Button, Input, Select, Checkbox, Radio, DatePicker } from 'ant-design-vue'

import Suggest from './extra-components/suggest'

// import DatePicker from './extra-components/date-picker'

const BUTTON = 'button'
const INPUT = 'input'
const SELECT = 'select'
const SUGGEST = 'suggest'
const RADIO_GROUP = 'radio-group'
const CHECKBOX_GROUP = 'checkbox-group'
const DATE_PICKER = 'date-picker'
const MONTH_PICKER = 'month-picker'
const DATE_RANGE = 'date-range-picker'
const MONTH_RANGE = 'month-range-picker'

export const controlType = {
  BUTTON,
  INPUT,
  SELECT,
  SUGGEST,
  RADIO_GROUP,
  CHECKBOX_GROUP,
  DATE_PICKER,
  MONTH_PICKER,
  DATE_RANGE,
  MONTH_RANGE
}
export const PRESET_CONTROL_TYPES = Object.keys(controlType).map(key => controlType[key])

export default {
  [BUTTON]: {
    component: Button
  },
  [INPUT]: {
    component: Input,
    initialValue: '',
    onChangeValueGetter: e => e.target.value
  },
  [SELECT]: {
    component: Select,
    initialValue: void 0
  },
  [SUGGEST]: {
    component: Suggest,
    initialValue: void 0
  },
  [RADIO_GROUP]: {
    component: Radio.Group,
    initialValue: null
  },
  [CHECKBOX_GROUP]: {
    component: Checkbox.Group,
    initialValue: []
  },
  [DATE_PICKER]: {
    component: DatePicker,
    initialValue: ''
  },
  [MONTH_PICKER]: {
    component: DatePicker.MonthPicker,
    initialValue: ''
  },
  [DATE_RANGE]: {
    component: DatePicker.RangePicker,
    initialValue: ''
  },
  [MONTH_RANGE]: {
    component: DatePicker.RangePicker,
    initialValue: ''
  }
}
