<!--搜索与重置-->
<template>
  <span>
    <!--左侧插槽-->
    <slot name="left" />
    <a-button
      v-if="crud.optShow.reset"
      type="default"
      @click="crud.resetQuery(crud.queryOnReset)">重置</a-button>
    <a-button type="primary" @click="crud.toQuery">查询</a-button>
    <!--右侧插槽-->
    <slot name="right" />
  </span>
</template>
<script>
import { crud } from '@/components/Crud/crud'
export default {
  mixins: [crud()],
  props: {
    itemClass: {
      type: String,
      required: false,
      default: ''
    }
  }
}
</script>
<style lang="less" scoped>
  button {
    margin-left: 8px;
  }
</style>
