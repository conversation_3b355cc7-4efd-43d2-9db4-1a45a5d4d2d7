<template>
  <div class="crud-opts">
    <span class="crud-opts-left">
      <span class="title">{{ crud.title }}</span>
      <!--左侧插槽-->
      <slot name="left" />
    </span>
    <div class="crud-opts-right">
      <!--      <a-icon type="menu" @click="toggleSearch()" />-->
      <!--右侧插槽-->
      <slot name="right" />
      <a-dropdown v-if="crud.optShow.download">
        <a-menu slot="overlay">
          <a-menu-item v-for="type in crud.download.types" :key="type">
            <a @click="crud.doExport(type)">{{ type }}</a>
          </a-menu-item>
        </a-menu>
        <a-button
          :loading="crud.downloadLoading"
          :disabled="!crud.data.length"
          @click="e => e.preventDefault()"
        >导出数据
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <refresh v-if="crud.optShow.refresh" width="14" height="14" @click="crud.refresh()" />
      <fullscreen v-if="crud.optShow.fullscreen" width="14" height="14" @click="toggleFullScreen()" />
      <a-popover
        placement="bottomRight"
        :getPopupContainer="() => $el"
        trigger="click"
        @visibleChange="columnsVisible"
      >
        <span>
          <setting v-if="crud.optShow.columns" width="22" height="22" />
        </span>
        <template slot="title">
          <a-checkbox
            v-model="allColumnsSelected"
            :indeterminate="allColumnsSelectedIndeterminate"
            @change="handleCheckAllChange"
          >
            列展示
          </a-checkbox>
          <span class="crud-opts-columns-reset-btn" @click="reset">重置</span>
        </template>
        <template slot="content">
          <div :key="tableColumnsKey" ref="sortable">
            <div
              v-for="item in tableColumns"
              :key="item.dataIndex"
              :data-index="item.dataIndex"
              :class="{ 'crud-opts-column-item': true, 'stable': !isMoving }">
              <draggable class="draggable-icon"/>
              <a-checkbox
                v-model="item.visible"
                @change="handleCheckedTableColumnsChange(item)"
              >
                {{ item.label }}
              </a-checkbox>
            </div>
          </div>
        </template>
      </a-popover>
    </div>
  </div>
</template>
<script>
import CRUD, { crud } from '@/components/Crud/crud'
import { getCustomColumns, saveCustomColumns } from './request'
import { fullscreen, refresh, setting, draggable } from '@/core/icons'
import Sortable from 'sortablejs'
import { objToFormData } from './utils'
// import _default from 'vuex'

export default {
  components: { fullscreen, refresh, setting, draggable },
  mixins: [crud()],
  props: {
    hiddenColumns: {
      type: Array,
      default: () => {
        return []
      }
    },
    ignoreColumns: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tableColumns: [],
      tableColumnsKey: 0,
      defaultTableColumns: [],
      allColumnsSelected: true,
      allColumnsSelectedIndeterminate: false,
      tableUnwatcher: null,
      // 忽略下次表格列变动
      ignoreNextTableColumnsChange: false,
      isMoving: false
    }
  },
  computed: {
    isLocalStorage() {
      return Object.prototype.toString.call(this.crud.optShow.columns) === '[object Boolean]'
    },
    localStorageKey() {
      return 'table_' + this.$route.fullPath
    }
  },
  watch: {
    'crud.props.table'() {
      this.initTableColumns()
      this.tableColumns.forEach(column => {
        if (this.hiddenColumns.indexOf(column.dataIndex) !== -1) {
          column.visible = false
          this.updateColumn(column)
        }
      })
    }
  },
  created() {
    this.crud.updateProp('searchToggle', true)
  },
  methods: {
    columnsVisible(visible) {
      if (visible) {
        this.bindSortable()
      } else {
        this.saveCustomColumns()
      }
    },
    bindSortable() {
      // 这里使用 setTimeout 为什么不是 nextTick 呢?
      // 这是由于 table.$parent.columns 的更新, 与当前组件的 nextTick 没有关系, 那么会使这里 nextTick 优先于父级 table.$parent 更新, 进而无法绑定最新 tableColumns.
      setTimeout(() => {
        if (this.$refs.sortable) {
          new Sortable(this.$refs.sortable, {
            animation: 150,
            onStart: () => {
              this.isMoving = true
            },
            onUpdate: (evt) => {
              this.sortWithRef(evt.to)
              this.isMoving = false
            }
          })
        }
      })
    },
    sortWithRef(ref) {
      const sortedList = Array.prototype.slice.call(ref.children)
      this.tableColumns = sortedList.map(columnRef => {
        const currentDataIndex = columnRef.getAttribute('data-index')
        return this.tableColumns.find(column => {
          return currentDataIndex === column.dataIndex
        })
      })
      this.updateColumn()
    },
    initTableColumns() {
      const table = this.crud.getTable()
      if (!table) {
        this.tableColumns = []
        return
      }
      let cols = null
      const columnFilter = e => e && this.ignoreColumns.indexOf(e.dataIndex) === -1
      const refCols = table.columns.filter(columnFilter)
      if (this.ignoreNextTableColumnsChange) {
        this.ignoreNextTableColumnsChange = false
        return
      }
      this.ignoreNextTableColumnsChange = false
      const columns = []
      cols = refCols
      cols.forEach(config => {
        const column = Object.assign(config, {
          dataIndex: config.dataIndex,
          label: config.title,
          visible: refCols.indexOf(config) !== -1
        })
        columns.push(column)
      })
      this.defaultTableColumns = columns
      this.fetchCustomColumns()
    },
    async fetchCustomColumns() {
      let columns = JSON.parse(JSON.stringify(this.defaultTableColumns))
      if (this.isLocalStorage) {
        const stringfy = localStorage.getItem(this.localStorageKey)
        if (stringfy) {
          columns = JSON.parse(stringfy)
        }
      } else {
        await getCustomColumns(this.crud.optShow.columns.getUrl, this.crud.optShow.columns.params).then(res => {
          columns = this.adaptorColumnsFromInterface(res.data && JSON.parse(res.data.headerKey)) || columns
          const table = this.crud.getTable()
          table.$el.removeAttribute('style')
        })
      }
      this.tableColumns = columns
      this.updateColumn()
    },
    saveCustomColumns() {
      if (this.isLocalStorage) {
        localStorage.setItem(this.localStorageKey, JSON.stringify(this.tableColumns))
      } else {
        const params = {
          columns: JSON.stringify(this.adaptorColumnsForInterface(this.tableColumns)),
          ...this.crud.optShow.columns.params
        }
        saveCustomColumns(this.crud.optShow.columns.saveUrl, objToFormData(params)).then(res => {
          this.defaultTableColumns = JSON.parse(JSON.stringify(this.tableColumns))
        })
      }
    },
    adaptorColumnsFromInterface(columns) {
      if (!columns || columns.length === 0) return
      const defaultColumns = this.defaultTableColumns
      return columns.sort((a, b) => Number(a.order) - Number(b.order)).reduce((acc, column) => {
        const _column = defaultColumns.find(_default => _default.title === column.name)
        if (!_column) return acc
        _column.visible = column.selectFlag === 'SELECTED'
        acc.push(_column)
        return acc
      }, [])
    },
    adaptorColumnsForInterface(columns) {
      return columns.map((column, index) => ({
        fixedFlag: 'FIXED_HEAD',
        name: column.title,
        order: index + 1,
        selectFlag: column.visible ? 'SELECTED' : 'UNSELECT'
      }))
    },
    toDelete(datas) {
      this.$confirm(`确认删除选中的${datas.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.crud.delAllLoading = true
        this.crud.doDelete(datas)
      }).catch(() => {
      })
    },
    handleCheckAllChange(e) {
      const val = e.target.checked
      if (val === false) {
        this.allColumnsSelected = true
        return
      }
      this.tableColumns.forEach(column => {
        if (!column.visible) {
          column.visible = true
          this.updateColumn()
        }
      })
      this.allColumnsSelected = val
      this.allColumnsSelectedIndeterminate = false
    },
    handleCheckedTableColumnsChange(item) {
      let totalCount = 0
      let selectedCount = 0
      this.tableColumns.forEach(column => {
        ++totalCount
        selectedCount += column.visible ? 1 : 0
      })
      if (selectedCount === 0) {
        this.crud.notify('请至少选择一列', CRUD.NOTIFICATION_TYPE.WARNING)
        this.$nextTick(function() {
          item.visible = true
        })
        return
      }
      this.allColumnsSelected = selectedCount === totalCount
      this.allColumnsSelectedIndeterminate = selectedCount !== totalCount && selectedCount !== 0
      this.updateColumn()
    },
    updateColumn() {
      const table = this.crud.props.table
      table.$parent.columns = this.tableColumns.filter(column => {
        return column.visible
      })
      this.tableColumnsKey += 1

      // after table refresh need to rebind sortable
      this.bindSortable()
      this.ignoreNextTableColumnsChange = true
    },
    reset() {
      this.tableColumns = JSON.parse(JSON.stringify(this.defaultTableColumns))
      this.updateColumn()
      this.allColumnsSelected = this.tableColumns.every(columns => columns.visible)
      this.allColumnsSelectedIndeterminate = !this.allColumnsSelected
    },
    toggleSearch() {
      this.crud.props.searchToggle = !this.crud.props.searchToggle
    },
    toggleFullScreen() {
      const table = this.crud.getTable().$el
      if (!table.fullscreenElement) {
        table.requestFullscreen()
      } else {
        if (table.exitFullscreen) {
          table.exitFullscreen()
        }
      }
    }
  }
}
</script>

<style lang="less">
.crud-opts {
  padding: 24px 0 10px 0;
  display: -webkit-flex;
  display: flex;
  align-items: center;

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.crud-opts .crud-opts-right {
  margin-left: auto;

  & > svg {
    margin-left: 20px;
    cursor: pointer;
  }
}
</style>
