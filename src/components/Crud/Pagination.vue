<!--分页-->
<template>
  <a-pagination
    v-if="page.total"
    v-model="page.page"
    :page-size.sync="page.size"
    :pageSizeOptions="['10', '20', '30', '40', '50', '100']"
    :total="page.total"
    style="margin-top: 8px;text-align: right;"
    show-size-changer
    layout="total, prev, pager, next, sizes"
    @showSizeChange="crud.sizeChangeHandler"
    @change="crud.pageChangeHandler"
  />
</template>
<script>
import { pagination } from '@/components/Crud/crud'
export default {
  mixins: [pagination()]
}
</script>
