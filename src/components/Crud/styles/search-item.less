.crud-search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0 18px 0;
  width: 33.33333%;

  .label {
    display: inline-block;
    width: 100px;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    padding-right: 8px;
    font-size: 14px;
    font-weight: 500;
  }

  .control {
    position: relative;
    width: 100%;

    .description {
      position: absolute;
      left: 0;
      top: 34px;
      white-space: nowrap;
    }

    &.is-range {
      // width: 380px;
      // padding-right: 20px;
    }

    &>*:first-child {
      width: 100% !important;
    }

  }
}

.crud-search-item:nth-child(3n-2) {
  padding-right: 30px;
}

.crud-search-item:nth-child(3n-1) {
  padding: 0 30px;
}

.crud-search-item:nth-child(3n) {
  padding-left: 30px;
}
