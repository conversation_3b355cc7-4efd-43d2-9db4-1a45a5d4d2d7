.crud-search-panel {
  display: flex;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;

  .content-wrapper {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
  }

  .action-wrapper {
    margin-left: auto;
  }

  //@include e(more-conditions) {
  //  margin: 8px 0;
  //
  //  @include m(content) {
  //    display: flex;
  //    flex-wrap: wrap;
  //    width: 626px;
  //    background: #FFF;
  //    padding: 10px 0;
  //    margin: 5px 0;
  //    border: 1px solid #EBEEF5;
  //    border-radius: 4px;
  //    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  //  }
  //}
}
