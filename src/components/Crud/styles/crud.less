@import "~ant-design-vue/es/style/themes/default.less";
@import "./search-panel";
@import "./search-item";

.filter-item {
  display: inline-block;
  vertical-align: middle;
  margin: 0 3px 10px 0;
}

.crud-opts {
  svg {
    cursor: pointer;
  }
}

.crud-opts-columns-reset-btn {
  font-size: 14px;
  cursor: pointer;
  color: @text-color-secondary;
  float: right;
  line-height: 21px;
}

.crud-opts-column-item {
  cursor: pointer;
  padding: 4px 0;
  user-select:none;
  position: relative;

  &.stable:hover {
    background-color: @primary-6;
    border-radius: 2px;
  }


  .draggable-icon {
    width: 10px;
    height: 10px;
    position: absolute;
    left: -14px;
    top: 9px;
  }
}