<template>
  <div>
    <a-upload
      :accept="acceptParam"
      action="/app/merchant/middle/netIn/fileUpload"
      :data="{ocrType: ocrType}"
      :headers="{'interaction-type': 'ERROR_CODE', 'X-WX-Id': xwxId || ''}"
      list-type="picture-card"
      :file-list="fileList"
      :before-upload="beforeUpload"
      @preview="handlePreview"
      @change="handleChange"
    >
      <div v-if="fileList.length < 1">
        <a-icon type="plus" />
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible = false">
      <img alt="example" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    ocrType: {
      type: String,
      default: ''
    },
    ocrSubType: {
      type: String,
      default: ''
    },
    urlName: {
      type: String,
      default: ''
    },
    maxSize: {
      type: Number,
      default: 5
    },
    // 当发生错误时，同时也触发 emit 事件，用于隔离组件在不同业务场景下的处理
    emitWhenFail: Boolean,
    // 成功时是否 toast 信息
    toastWhenSuccess: Boolean,
    acceptParam: {
      type: String,
      default: '.jpg,.jpeg,.png,.bmp'
    }
  },
  data() {
    return {
      fileList: [],
      previewImage: '',
      previewVisible: false,
      xwxId: window.localStorage.getItem('X-WX-Id')
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.showPicture()
        } else {
          this.fileList = []
        }
      },
      immediate: true
    }
  },
  methods: {
    // 预览上传的图片
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj)
      }
      this.previewImage = this.value || file.preview
      this.previewVisible = true
    },
    // 上传回调
    handleChange(info) {
      // 判断登录失效接口会返回401，跳转登录界面
      if (typeof info.file.response === 'string' && info.file.response.indexOf('401') > -1) {
        const origin = window.location.origin
        window.location.href = `${origin}/auth/signin?redirectUrl=${origin}/mp-merchant-access/index.html#/`
      }
      if (info.file.status === 'done') {
        if (info.file.response.status === 'success') {
          const url = info.file.response.data ? info.file.response.data.fileUrl : ''
          this.$emit('input', url)
          this.$emit('clearValidate', this.urlName)

          let responseData = { ...info.file.response.data }
          const ocrSuccessMsg = '识别成功，请确认识别结果'
          const ocrErrorMsg = '识别失败，请手动填写内容'

          if (this.ocrType === 'ID_CARD') {
            if (this.toastWhenSuccess) {
              const { name, expireBeginDate } = responseData
              // 为与 h5 保持一致
              const isError = this.ocrSubType === 'ID_CARD_BACK' ? !expireBeginDate : !name
              !isError && this.$message.success(ocrSuccessMsg)
              isError && this.$message.error(ocrErrorMsg)
              if (isError) responseData = {}
            }
            this.$emit('getCardInfo', responseData)
            this.$emit('getContactInfo', responseData)
            this.$emit('getUboInfo', responseData)
          } else if (this.ocrType === 'ENTERPRISE') {
            if (this.toastWhenSuccess) {
              const { name } = responseData
              // 为与 h5 保持一致
              const isError = !name
              !isError && this.$message.success(ocrSuccessMsg)
              isError && this.$message.error(ocrErrorMsg)
              if (isError) responseData = {}
            }
            this.$emit('getBusinessInfo', responseData)
          } else if (this.ocrType === 'BANKCARD') {
            if (this.toastWhenSuccess) {
              const { bankCode, bankCardNo } = responseData
              const isError = !bankCode || !bankCardNo
              !isError && this.$message.success(ocrSuccessMsg)
              isError && this.$message.error(ocrErrorMsg)
              if (isError) responseData = {}
            }
            this.$emit('getBankCardInfo', responseData)
          } else if (this.ocrType === 'FPRP') {
            if (this.toastWhenSuccess) {
              const { name } = responseData
              const isError = !name
              !isError && this.$message.success(ocrSuccessMsg)
              isError && this.$message.error(ocrErrorMsg)
              if (isError) responseData = {}
            }
            this.$emit('getCardInfo', responseData)
          }
        } else {
          if (this.emitWhenFail) {
            const responseData = { }
            if (this.ocrType === 'ID_CARD' || this.ocrType === 'FPRP') {
              this.$emit('getCardInfo', responseData)
            } else if (this.ocrType === 'ENTERPRISE') {
              this.$emit('getBusinessInfo', responseData)
            } else if (this.ocrType === 'BANKCARD') {
              this.$emit('getBankCardInfo', responseData)
            }
          }

          this.$message.error(info.file.response.errMsg)
        }
      }
      if (info.file.status) {
        this.fileList = info.fileList
      }
      if (info.file.status === 'removed') {
        this.$emit('input', '')
      }
    },
    // 回显图片
    showPicture() {
      !this.fileList.length && this.fileList.push({
        uid: '-1',
        name: 'image.png',
        status: 'done',
        url: this.value
      })
    },
    // 上传前限制大小
    beforeUpload(file) {
      const imgSize = file.size / 1024 / 1024
      if (imgSize > this.maxSize) {
        this.$message.error(`图片需${this.maxSize}M以内!`)
        return false
      }
      const validTypes = ['image/jpeg', 'image/png', 'image/bmp']
      if (!validTypes.includes(file.type)) {
        this.$message.error('所选格式不支持')
        return false
      }
      return true
    }
  }
}
</script>
