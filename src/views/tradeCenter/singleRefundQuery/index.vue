<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowCode'
export default defineComponent({
  name: 'MpSingleRefundQuery',
  data() {
    return {
      pageCode: 'mpSingleRefundQuery',
      schema: {} as any
    }
  },
  methods: {
    routeTo(path: string, query: string) {
      this.$router.push({
        path,
        query: query ? JSON.parse(query) : {}
      })
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @routeTo="routeTo"/>
  </div>
</template>
