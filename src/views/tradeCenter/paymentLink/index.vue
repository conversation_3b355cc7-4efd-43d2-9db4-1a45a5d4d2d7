<!-- 生成付款链接 -->
<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import FormModel from '@/components/FormModel/index.vue'
import '@/assets/fontAL/iconfont.css' // TODO:先局部用不放main.js里，防止污染其他

import { createService } from '@/utils/http'
// 配置成功码，包含业务错误码，让所有响应都进入 then 而不是 catch
const request = createService('/gov-web', ['SUCCESS', '000000', '200', 200, 'P00017'])

export default defineComponent({
  name: 'MpPaymentLink',
  components: {
    FormModel
  },
  data() {
    return {
      copyUrlVisible: false,
      paymentUrl: '',
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      form: {
        goodsName: '',
        idCard: '',
        memo: '',
        orderAmount: '',
        orderNo: '',
        payer: ''
      },
      rules: {
        orderNo: [{ required: true, message: '请生成订单号' }],
        orderAmount: [{ required: true, message: '请输入金额', trigger: 'change' }],
        goodsName: [{ required: true, message: '请输入考试名称', trigger: 'blur' }]
      },
      formItems: [
        {
          label: '商户订单号',
          key: 'orderNo',
          type: 'input',
          props: {
            placeholder: '点击生成订单号'
          }
        },
        {
          label: '订单金额(元)',
          key: 'orderAmount',
          type: 'inputNumber',
          suffix: '元',
          props: {
            placeholder: '请输入订单金额'
          }
        },
        {
          label: '考试名称',
          key: 'goodsName',
          type: 'input',
          max: 64,
          props: {
            placeholder: '请输入考试名称'
          }
        },
        {
          label: '考生姓名',
          key: 'payer',
          type: 'input',
          max: 32,
          props: {
            placeholder: '如需开票请填写此项'
          }
        },
        {
          label: '考生证件号',
          key: 'idCard',
          type: 'input',
          max: 64,
          props: {
            placeholder: '如需开票请填写此项'
          }
        },
        {
          label: '对账备注',
          key: 'memo',
          type: 'textarea',
          max: 30,
          props: {
            placeholder: '选填',
            rows: 3
          }
        }
      ],
      buttons: [
        {
          text: '生成支付链接',
          type: 'primary',
          action: 'generatePayLink'
        }
      ]
    }
  },
  methods: {
    // 处理表单字段变更
    handleFieldChange(key: string, value: any) {
      this.$set(this.form, key, value)
    },
    clearValidate(field: string) {
      const formRef = this.$refs.formModelRef as any
      if (formRef && formRef.clearValidate) {
        formRef.clearValidate([field])
      }
    },
    generateOrderNo(cb?: Function) {
      request({
        url: '/mp/gen/orderNo',
        method: 'get'
      })
        .then((response: any) => {
          const res = response.data

          if (res.msg !== 'success') {
            this.$message.error(res.msg)
          }
          if (res.msg === 'success') {
            this.handleFieldChange('orderNo', res.data)
            this.clearValidate('orderNo')// 清除订单号字段的验证错误
            cb && cb()
          }
        })
        .catch((err: any) => {
          console.log(err)
        })
    },
    getUrl() {
      request({
        url: '/mp/gen/url',
        method: 'post',
        data: {
          ...this.form
        }
      })
        .then((response: any) => {
          const res = response.data
          if (res.code === '000000' && res.msg === 'success') {
            this.paymentUrl = res.data
            this.copyUrlVisible = true
          } else if (res.code === 'P00017') {
            // "订单金额不一致"
            this.generateOrderNo(() => {
              this.getUrl()
            })
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err: any) => {
          console.log(err)
        })
    },
    // 处理弹窗确定按钮
    handleOk() {
      this.copyUrlVisible = false
    },
    // 复制链接到剪贴板
    copyToClipboard() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.paymentUrl).then(() => {
          this.$message.success('复制成功')
        }).catch(() => {
          this.fallbackCopyTextToClipboard()
        })
      } else {
        this.fallbackCopyTextToClipboard()
      }
    },
    // 兼容性复制方法
    fallbackCopyTextToClipboard() {
      const textArea = document.createElement('textarea')
      textArea.value = this.paymentUrl
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('复制成功')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    },
    generatePayLink() {
      const formRef = this.$refs.formModelRef as any
      if (formRef.validateField) {
        formRef.validateField((valid: any) => {
          if (valid) {
            this.getUrl()
          } else {
            return false
          }
        })
      }
    },
    onButtonClick(button: any, formData: any) {
      // event：generatePayLink
      this[button.action](formData)
    }
  },
  async mounted() {
    this.generateOrderNo()
  }
})
</script>
<template>
  <div class="payment-link-view">
    <FormModel
      ref="formModelRef"
      :form="form"
      :form-items="formItems"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :buttons="buttons"
      style="max-width: 800px;"
      @button-click="onButtonClick"
      @field-change="handleFieldChange"
    >
      <!-- 只为商户订单号字段添加生成按钮 -->
      <template #orderNo="{ item, value, updateValue }">
        <div style="display: flex; gap: 8px; align-items: center;">
          <a-col span="24">
            <a-input
              :value="value"
              v-bind="item.props"
              disabled
              @input="updateValue($event.target.value)"
            />
          </a-col>
          <a-button
            type="primary"
            @click="generateOrderNo"
          >
            生成订单号
          </a-button>
        </div>
      </template>

    </FormModel>
    <a-modal
      v-model="copyUrlVisible"
      class="payment-link-model-body"
      width="684px"
      title="生成结果"
      ok-text="复制"
      cancel-text="取消"
      centered
      @ok="copyToClipboard"
      @cancel="handleOk">
      <div class="modal-body">
        <i class="iconfont iconchenggong" />
        <h3>支付链接生成成功！</h3>
        <p class="p">
          复制链接发送给付款方，付款方在浏览器中访问该链接，即可通过支付网关付款给您。<br/>您可以在交易查询该笔订单的支付状态
        </p>
        <div class="url">
          {{ paymentUrl }}
        </div>
      </div>
    </a-modal>
  </div>
</template>
<style lang="less">
.payment-link-view{
  padding-top: 3%;
}
.payment-link-model-body{
  .modal-body{
    text-align: center;
  }
  .ant-modal-body{
    padding: 42px 20px;
  }
  .iconchenggong {
    font-size: 48px;
    color: #52c41a;
    margin-bottom: 15px;
  }
  h3 {
    font-size: 18px;
    margin-bottom: 16px;
  }
  p {
    margin: auto;
    color: #8c8c8c;
    margin-bottom: 24px;
  }
  .url{
    background: #f5f5f5;
    padding: 13px 19px 13px;
    // padding: 13px 73px 19px 19px;
    border-radius: 6px;
    word-break: break-all;
    text-align: left;
    font-size: 12px;
  }
}
</style>
