<!-- 交易汇总 -->
<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowCode'
export default defineComponent({
  name: 'MpTradeSummary',
  data() {
    return {
      pageCode: 'mpTradeSummary',
      schema: {} as any
    }
  },
  methods: {
    routeTo(path: string) {
      this.$router.push({
        path
      })
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @routeTo="routeTo"/>
  </div>
</template>
