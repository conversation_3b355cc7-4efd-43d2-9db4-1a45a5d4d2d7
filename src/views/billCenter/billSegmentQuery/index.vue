<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowCode'
export default defineComponent({
  name: 'MpBillSegmentQuery',
  data() {
    return {
      pageCode: 'mpBillSegmentQuery',
      schema: {} as any
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" />
  </div>
</template>
