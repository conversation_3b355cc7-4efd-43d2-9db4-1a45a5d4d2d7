<!-- 票据冲红准备 -->
<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { createService } from '@/utils/http'
const request = createService('/gov-web')

export default defineComponent({
  name: 'MpPreparatioRedRush',
  data() {
    return {
      fileList: [] as any[],
      uploading: false
    }
  },
  methods: {
    handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.error('请先选择文件')
        return
      }
      // 上传逻辑
      request({
        url: '/mp/ah/importInvoiceWriteOffFile',
        method: 'get'
      })
        .then((response: any) => {
          const res = response.data
          const data = res.data

          if (res.msg !== 'success') {
            this.$message.error(res.msg)
          }
          if (res.msg === 'success') {
          }
        })
        .catch((err: any) => {
          console.log(err)
        })
      console.log('开始上传', this.fileList)
    },
    beforeUpload(file: any) {
      this.fileList = [...this.fileList, file]
      return false
    },
    onRemove(file: any) {
      const index = this.fileList.indexOf(file)
      const newFileList = this.fileList.slice()
      newFileList.splice(index, 1)
      this.fileList = newFileList
    },
    downloadTemplate() {
      // 下载模板逻辑
      console.log('下载模板')
    },
    downloadCenter() {
      // 跳转到下载中心
      console.log('跳转到下载中心')
    }
  },
  async mounted() {

  }
})
</script>
<template>
  <div class="preparationRedRushView">
    <div class="upload-container">
      <!-- 左侧内容区域 -->
      <div class="upload-content">
        <!-- 步骤一 -->
        <div class="step-section">
          <div class="step-title">第一步：下载导入模板</div>
          <div class="step-description">
            请先下载模板配置文件，按照模板格式进行填写后再进行<a-button type="link" @click="downloadTemplate">下载模板</a-button>
          </div>
        </div>
        <a-divider />
        <!-- 步骤二 -->
        <div class="step-section">
          <div class="step-title">第二步：上传票据冲红准备，等待解析结果</div>
          <div class="step-description">
            上传开票解析成功后，可点击<a-button type="link" @click="downloadCenter">下载中心</a-button>查看解析结果及历史记录
          </div>
        </div>

        <!-- 上传区域 -->
        <div class="upload-area">
          <a-upload-dragger
            name="file"
            :multiple="false"
            :file-list="fileList"
            :before-upload="beforeUpload"
            :remove="onRemove"
            accept=".xlsx,.xls"
          >
            <div class="upload-icon">
              <a-icon type="inbox" style="font-size: 48px; color: #52c41a;" />
            </div>
            <div class="upload-text">
              <div class="upload-main-text">点击或将文件拖拽到这里上传</div>
              <div class="upload-hint">
                建议冲红处备文件类型为XLS格式，文件上传时间取决于文件大小请耐心等待
              </div>
            </div>
          </a-upload-dragger>
        </div>

        <!-- 上传按钮 -->
        <div class="upload-button-area">
          <a-button
            type="primary"
            size="large"
            :loading="uploading"
            @click="handleUpload"
          >
            确认上传
          </a-button>
        </div>
      </div>

      <!-- 右侧图片区域 -->
      <div class="upload-illustration">
        <img src="" alt="上传插图" class="illustration-image" />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.preparationRedRushView {
  // width: 100%;
  // padding: 24px;

  .upload-container {
    min-height: 100vh;
    display: flex;
    // max-width: 1200px;
    margin: 0 auto;
    background: white;
    // border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .upload-content {
    width: 50%;
    padding: 88px 88px 0 80px;
  }

  .step-section {
    margin-bottom: 20px;
    line-height: 22px;
    font-size: 15px;
    .step-title {
      font-weight: 600;
      color: #262626;
      // margin-bottom: 5px;
    }

    .step-description {
      // font-size: 15px;
      color: #8c8c8c;
      .ant-btn-link{
        padding: 0 4px;
        font-size: 15px;
        font-weight: 500;
      }
    }
  }

  .upload-area {
    margin: 20px 0 16px 0;
    /deep/ .ant-upload-drag {
      border-radius: 8px;
      // padding: 26px 16px 23px 16px;
    }

    .upload-icon {
      margin-bottom: 10px;
    }

    .upload-text {
      .upload-main-text {
        font-size: 16px;
        color: #262626;
        margin-bottom: 8px;
      }

      .upload-hint {
        font-size: 12px;
        color: #8c8c8c;
        line-height: 1.4;
      }
    }
  }

  .upload-button-area {
    /deep/ .ant-btn {
      height: 34px;
      padding: 0 28px;
      font-size: 14px;
    }
  }

  .upload-illustration {
    padding: 88px 55px 0 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    .illustration-image {
      object-fit: contain;
    }
  }
}
</style>
