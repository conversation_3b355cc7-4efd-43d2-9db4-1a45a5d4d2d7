<!-- 发起分账汇总查询 -->
<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowCode'
export default defineComponent({
  name: 'MpInitiateSummaryQuery',
  data() {
    return {
      pageCode: 'mpInitiateSummaryQuery',
      schema: {} as any
    }
  },
  methods: {
    routeTo(path: string) {
      this.$router.push({
        path
      })
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" :schema="schema" @routeTo="routeTo"/>
  </div>
</template>
