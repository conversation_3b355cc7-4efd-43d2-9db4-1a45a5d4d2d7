<!-- 发起分账明细查询 -->
<script lang="ts">
import { defineComponent } from '@vue/composition-api'
import Vue from 'vue'
import { fetchSchema } from '@/utils/lowCode'
import { createService } from '@/utils/http'
const request = createService('/gov-web')

interface ComponentInstance extends Vue {
  tableData: any[];
  getRowSpan: (orderNo: string, splitOrderType: string) => number;
  isSameOrderNo: (row1: any, row2: any) => boolean;
}

// 通用的 customRender 函数
const customRender = (
  value: any,
  row: any,
  index: number,
  tableData: any[],
  getRowSpan: (orderNo: string, splitOrderType: string) => number,
  isSameOrderNo: (row1: any, row2: any) => boolean
) => {
  const obj = {
    children: value, // 默认显示字段的值
    attrs: { rowSpan: {} }
  }

  // 如果是第一行，或者当前行与上一行的 orderNo 不相同，则根据 orderNo 的数量设置 rowSpan
  if (index === 0 || !isSameOrderNo(row, tableData[index - 1])) {
    obj.attrs.rowSpan = getRowSpan(row.orderNo, row.splitOrderType) // 设置行数
  } else {
    obj.attrs.rowSpan = 0 // 合并行，设置 rowSpan 为 0
  }

  return obj
}

export default defineComponent({
  name: 'MpInitiateDetailQuery',
  data() {
    return {
      pageCode: 'mpInitiateDetailQuery',
      schema: {} as any,
      loading: false,
      pagination: {
        current: 1,
        pageSize: 20,
        pages: 1,
        total: 0,
        pageSizeOptions: ['10', '20', '30', '40', '50', '100'],
        showSizeChanger: true,
        showQuickJumper: true
      },
      columns: [
        // {
        //   title: '序号',
        //   dataIndex: 'index',
        //   key: 'index',
        //   customRender: (value: any, row: any, index: number) => {
        //     const instance = this as unknown as ComponentInstance // 强制类型转换
        //     value = index
        //     return customRender(
        //       value,
        //       row,
        //       index,
        //       instance.tableData, // 现在 TypeScript 能识别 tableData
        //       instance.getRowSpan,
        //       instance.isSameOrderNo
        //     )
        //   }
        // },
        {
          title: '商户订单号',
          dataIndex: 'orderNo',
          key: 'orderNo',
          // width: 150,
          customRender: (value: any, row: any, index: number) => {
            const instance = this as unknown as ComponentInstance // 强制类型转换
            return customRender(
              value,
              row,
              index,
              instance.tableData, // 现在 TypeScript 能识别 tableData
              instance.getRowSpan,
              instance.isSameOrderNo
            )
          }
        }, {
          title: '考试名称',
          dataIndex: 'rsExamName',
          key: 'rsExamName',
          // width: 150,
          customRender: (value: any, row: any, index: number) => {
            const instance = this as unknown as ComponentInstance // 强制类型转换
            return customRender(
              value,
              row,
              index,
              instance.tableData, // 现在 TypeScript 能识别 tableData
              instance.getRowSpan,
              instance.isSameOrderNo
            )
          }
        }, {
          title: '订单类型',
          dataIndex: 'splitOrderType',
          key: 'splitOrderType',
          // width: 150,
          customRender: (value: any, row: any, index: number) => {
            const instance = this as unknown as ComponentInstance // 强制类型转换
            // const tagProps = {
            //   color: row.splitOrderType === 'PAY' ? '#7CC66E' : '#349FFF'
            // }
            const tagText = row.splitOrderType === 'PAY' ? '支付' : '退款'
            value = tagText

            return customRender(
              // (h: any) => {
              //   console.log(111111, h('a-tag', { props: tagProps }, tagText))
              //   // 直接创建 a-tag 组件
              //   return 121212
              // },
              value,
              row,
              index,
              instance.tableData, // 现在 TypeScript 能识别 tableData
              instance.getRowSpan,
              instance.isSameOrderNo
            )
          }
        }, {
          title: '订单金额(元)',
          dataIndex: 'orderAmount',
          key: 'orderAmount',
          // align: 'right',
          // width: 150,
          customRender: (value: any, row: any, index: number) => {
            const instance = this as unknown as ComponentInstance // 强制类型转换
            return customRender(
              value,
              row,
              index,
              instance.tableData, // 现在 TypeScript 能识别 tableData
              instance.getRowSpan,
              instance.isSameOrderNo
            )
          }
        }, {
          title: '接收方商编',
          dataIndex: 'toMerchantNo',
          key: 'toMerchantNo'
          // width: 150
        }, {
          title: '接收方名称',
          dataIndex: 'toMerchantName',
          key: 'toMerchantName'
          // width: 150
        }, {
          title: '分账金额(元)',
          dataIndex: 'splitAmount',
          key: 'splitAmount'
          // width: 150
        }, {
          title: '下单时间',
          dataIndex: 'orderCreateTime',
          key: 'orderCreateTime',
          // width: 150,
          customRender: (value: any, row: any, index: number) => {
            const instance = this as unknown as ComponentInstance // 强制类型转换
            return customRender(
              value,
              row,
              index,
              instance.tableData, // 现在 TypeScript 能识别 tableData
              instance.getRowSpan,
              instance.isSameOrderNo
            )
          }
        }
      ],
      tableData: [] as any
    }
  },
  computed: {
    renderer() {
      // 其中 my_renderer 为上文自定义的渲染器id
      return this[`__yeepay_lowcode_${this.pageCode}__`].value
    }
  },

  methods: {
    refreshList() {
      this.loading = true
      const pages_components = this.renderer.schema.components

      const queriesList = this.renderer.schema.queries

      const isZwSplit = queriesList.filter((item: any) => {
        return item.name === 'isZwSplit'
      })

      const type = pages_components.search_order_type.exposedVariables.value === '__yeepay_lowcode_empty_string__' ? '' : pages_components.search_order_type.exposedVariables.value

      request({
        url: '/mp/splitBiz/initiator/detail',
        method: 'post',
        data: {
          endTime: pages_components.search_pay_range.exposedVariables.value[1],
          orderNo: pages_components.search_order_number.exposedVariables.value,
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          rsExamName: pages_components.search_exam_name.exposedVariables.value,
          splitBusinessType: isZwSplit[0]?.rawData.data?.data ? 'ZW' : 'ZT',
          splitOrderType: type,
          staTime: pages_components.search_pay_range.exposedVariables.value[0],
          toMerchantName: pages_components.search_recieve_name.exposedVariables.value
        }
      })
        .then((response: any) => {
          this.loading = false

          const res = response.data
          const data = res.data

          if (res.msg !== 'success') {
            this.$message.error(res.msg)
          }
          if (res.msg === 'success') {
            this.pagination = {
              ...this.pagination,
              total: data.total,
              pages: data.pages
            }
            const list = data.list
            const newList: any = []
            list.forEach((item: any) => {
              item.receiverDetail.forEach((detailItem: any) => {
                newList.push({
                  ...item,
                  ...detailItem
                })
              })
            })
            this.tableData = newList
          }
        })
        .catch((err: any) => {
          this.loading = false
          console.log(err)
        })
    },
    /** 统计查询 */
    refreshCount() {
      const pages_components = this.renderer.schema.components

      const queriesList = this.renderer.schema.queries

      const isZwSplit = queriesList.filter((item: any) => {
        return item.name === 'isZwSplit'
      })

      const type = pages_components.search_order_type.exposedVariables.value === '__yeepay_lowcode_empty_string__' ? '' : pages_components.search_order_type.exposedVariables.value

      const pms = {
        endTime: pages_components.search_pay_range.exposedVariables.value[1],
        orderNo: pages_components.search_order_number.exposedVariables.value,
        pageNum: this.pagination.current,
        pageSize: this.pagination.pageSize,
        rsExamName: pages_components.search_exam_name.exposedVariables.value,
        splitBusinessType: isZwSplit[0]?.rawData.data?.data ? 'ZW' : 'ZT',
        splitOrderType: type,
        staTime: pages_components.search_pay_range.exposedVariables.value[0],
        toMerchantName: pages_components.search_recieve_name.exposedVariables.value
      }

      request({
        url: '/mp/splitBiz/initiator/count',
        method: 'post',
        data: pms
      })
        .then((response: any) => {
          const res = response.data
          const data = res.data
          if (res.msg === 'success') {
            const formatNumber = (num: any) => {
              const parts = num.toString().split('.')
              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              return parts.join('.')
            }
            const tm = data.totalAmount > 0 ? formatNumber(data.totalAmount) : 0.00
            const tc = data.totalCount > 0 ? formatNumber(data.totalCount) : 0
            const str = `<div style="font-weight:">统计查询结果: 分账总金额<span style="color:#F36826FF"> ` + tm + ` </span>元 ｜ 共 <span style="color:#F36826FF"> ` + tc + `</span> 笔</div>`
            pages_components.typography_1 && (pages_components.typography_1.exposedVariables.value = str)
            pages_components.typography_2 && (pages_components.typography_2.exposedVariables.value = str)
          }
        })
        .catch((err: any) => {
          console.log(err)
        })
    },
    init(res: any) {
      this.loading = false
      const table_1 = this.renderer.schema.components?.table_1?.exposedVariables
      this.pagination = {
        ...this.pagination,
        total: res.total,
        pages: res.pages,
        current: table_1.pageIndex,
        pageSize: table_1.pageSize
      }

      const list = res.list
      const newList: any = []
      list.forEach((item: any) => {
        item.receiverDetail.forEach((detailItem: any) => {
          newList.push({
            ...item,
            ...detailItem
          })
        })
      })
      this.tableData = newList
    },

    // 仅比较订单号是否相同
    isSameOrderNo(currentLine: any, nextLine: any): boolean {
      return currentLine.orderNo === nextLine.orderNo && currentLine.splitOrderType === nextLine.splitOrderType
    },

    // 获取每个订单号的行合并数
    getRowSpan(orderNo: string, splitOrderType: string): number {
      let count = 0
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].orderNo === orderNo && this.tableData[i].splitOrderType === splitOrderType) {
          count++
        }
      }
      return count
    },
    showTotal(total: number) {
      return `共 ${total} 条记录 第 ${this.pagination.current} / ${this.pagination.pages} 页`
    },
    /** 切换条数 */
    onShowSizeChange(current: number, pageSize: number) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.refreshList()
      this.refreshCount()
    },
    /** 切换页码 */
    handleTableChange(current: number) {
      this.pagination.current = current
      this.refreshList()
      this.refreshCount()
    }
  },

  async mounted() {
    this.loading = true
    fetchSchema(this.pageCode).then((res: {}) => {
      this.schema = res
      // 获取 dom 元素自定义事件
      this.$nextTick(() => {
        // 注册低代码平台事件
        this.renderer.register('initList', this.init)
      })
    }).catch(() => {
      this.loading = false
    })
  }
})
</script>

<template>
  <div class="p-5">
    <low-code-renderer v-if="schema.components" id="mpInitiateDetailQuery" :schema="schema">
      <template #slot_1>
        <a-table
          :columns="columns"
          :data-source="tableData"
          bordered
          size="middle"
          class="ant-table-fixed"
          :pagination="false"
          :loading="loading"
        />
        <div>
          <a-pagination
            v-model="pagination.current"
            show-size-changer
            :total="pagination.total"
            :page-size="pagination.pageSize"
            :page-size-options="pagination.pageSizeOptions"
            :show-total="showTotal"
            @change="handleTableChange"
            @showSizeChange="onShowSizeChange"

          />
        </div>
      </template>
    </low-code-renderer>
  </div>
</template>
<style>
.ant-pagination {
  display: flex!important;
  justify-content: right!important;
  align-items: center!important;
  position: relative!important;
  margin-top: 8px!important;
}
.ant-pagination .ant-pagination-total-text{
  position: absolute!important;
  left: 0!important;
}
</style>
