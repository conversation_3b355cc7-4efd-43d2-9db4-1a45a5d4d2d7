exports.proxy = {
  '/gov-web': {
    // 'target': 'http://172.18.162.114:8080/merchant-portal-server/api/v1',
    target: 'https://qamp.yeepay.com',
    // 'target': 'http://172.18.162.53:8088',
    changeOrigin: true,
    ws: false
  },
  '/merchant-portal-server': {
    // 'target': 'http://172.18.162.114:8080/merchant-portal-server/api/v1',
    // 'target': 'http://ycetest.yeepay.com:30718',
    target: 'https://qamp.yeepay.com',
    changeOrigin: true,
    ws: false
  },
  '/pmc-boss': {
    target: 'http://qafmc.yeepay.com:30868/pmc-boss',
    changeOrigin: true,
    pathRewrite: {
      '^/pmc-boss': ''
    }
  },
  '/app': {
    // 'target': 'http://172.18.162.114:8080/merchant-portal-server/api/v1',
    // 'target': 'http://ycetest.yeepay.com:30718',
    target: 'https://qamp.yeepay.com',
    changeOrigin: true,
    ws: false
  },
  '/lowcode-mp-app-server': {
    'target': 'https://qamp.yeepay.com',
    'pathRewrite': {
      '^/lowcode-mp-app-server': '/app'
    },
    'changeOrigin': true,
    'ws': false
  },
  '/trade-mp-app': {
    'target': 'https://qamp.yeepay.com',
    'changeOrigin': true,
    'ws': false
  },
  '/trade-boss-server': {
    'target': 'https://qaboss.yeepay.com',
    'changeOrigin': true,
    'ws': false
  }
}
