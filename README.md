# mp-electronic-government

## 开发规范
1. 组件命名使用大驼峰命名法（PascalCase）
2. 路由名称与组件名保持一致
3. 使用 ESLint 进行代码规范检查
4. 提交代码前必须通过 lint 检查

## Git 提交规范
提交信息必须符合规范，例如：
- feat: 新功能
- fix: 修复问题
- docs: 修改文档
- style: 修改代码格式
- refactor: 代码重构
- perf: 优化相关
- test: 测试用例
- chore: 其他修改

## 注意事项
1. 提交代码前请确保通过 ESLint 检查
2. 遵循 Git 提交信息规范
3. 保持代码结构清晰，遵循组件化开发原则
4. 及时更新文档

## 常见问题
如遇到问题，请参考以下步骤：
1. 检查 node 版本是否符合要求
2. 清除依赖重新安装：`pnpm install`
3. 清除缓存：`pnpm clean:cache`
4. 启动项目：`pnpm serve`

## 联系方式
如有问题请联系项目负责人或提交 Issue。

## 项目结构

主要目录说明：
- `public/`: 存放静态资源，包含 HTML 模板
- `src/`: 源代码目录
  - `api/`: 后端接口定义
  - `components/`: 可复用的组件
  - `core/`: 核心配置和初始化
  - `router/`: 路由配置，包含权限控制
  - `store/`: Vuex 状态管理
  - `views/`: 业务页面组件
  - `mixin/`: 混合
  - `utils/`: 工具函数
- `scripts/`: 工具脚本，如项目初始化
- `.husky/`: Git hooks，用于代码提交检查
- `package.json`: 项目配置和依赖管理
- `main.ts`: 项目入口文件
- `App.vue`: 项目主组件
- `main.js`: 项目主入口文件
- `vue.config.js`: 项目配置
- `babel.config.js`: babel 配置
- `postcss.config.js`: postcss 配置
- `eslint.config.js`: eslint 配置
- `prettier.config.js`: prettier 配置





